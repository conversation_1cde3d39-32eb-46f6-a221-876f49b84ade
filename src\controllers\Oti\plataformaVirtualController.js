import { checkAtributosNecesarios } from "../../utils/checkAtributosNecesarios.js";
import uuidValidate from "uuid-validate";
import PlataformaVirtual from "../../models/Oti/plataformaVirtual.js";
import { checkDataType } from "../../utils/checkDataType.js";
import Usuario from "../../models/Usuario/usuario.js";
import Acceso from "../../models/Usuario/acceso.js";
import { Op } from "sequelize";
import InfraestructuraRecurso from "../../models/Oti/InfraestructuraRecurso.js";
import { checkToken, checkTokenLocal } from "../../auth/auth.js";
import validateStringsAZ from "../../utils/validateStringAtoZ.js";
import { sequelize } from "../../database/database.js";
import { checkPermisosUsuario } from "../../utils/checkPermisosUsuario.js";

const checkDatabase = async () => {
  try {
    console.log("Checking database connection...");
    await sequelize.authenticate();
    console.log("Database is connected successfully.");
  } catch (error) {
    console.error("Database connection error:", error);
    return res.status(400).json({ message: "Database not connected" });
  }
};

/**
 * Fetch a specific plataformaVirtual by plataforma_id.
 */
export const getPlataformaVirtual = async (req, res) => {
  const userToken = req.headers.authorization;
  const { plataforma_id } = req.params;

  try {
    // Check the database connection
    await checkDatabase();
    if (!uuidValidate(plataforma_id)) {
      return res.status(400).json({ message: "Invalid plataforma_id" });
    }
    // check if the token is valid and not expired
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    const plataforma = await PlataformaVirtual.findByPk(plataforma_id);
    if (!plataforma) {
      return res.status(404).json({ message: "PlataformaVirtual not found" });
    }

    res.status(200).json(plataforma);
  } catch (error) {
    console.error("Error fetching plataformaVirtual:", error);
    res.status(500).json({ message: "Error fetching plataformaVirtual" });
  }
};

/**
 * Fetch all plataformasVirtuales associated with a specific infraestructuraRecurso_id.
 */
export const getAllPlataformaVirtual = async (req, res) => {
  const userToken = req.headers.authorization;
  const { infraestructuraRecurso_id } = req.params;

  try {
    // Check the database connection
    await checkDatabase();
    if (!uuidValidate(infraestructuraRecurso_id)) {
      return res
        .status(400)
        .json({ message: "Invalid infraestructuraRecurso_id" });
    }

    // check if the token is valid and not expired
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }

    const infraestructuraRecurso = await InfraestructuraRecurso.findByPk(
      infraestructuraRecurso_id
    );
    if (!infraestructuraRecurso) {
      return res
        .status(404)
        .json({ message: "InfraestructuraRecurso not found" });
    }

    const plataformas = await PlataformaVirtual.findAll({
      where: { infraestructuraRecurso_id },
    });

    if (plataformas.length === 0) {
      return res.status(404).json({ message: "No plataformas found" });
    }

    res.status(200).json(plataformas);
  } catch (error) {
    console.error("Error fetching all plataformasVirtuales:", error);
    res.status(500).json({ message: "Error fetching plataformasVirtuales" });
  }
};

/**
 * Create a new plataformaVirtual.
 */
export const postPlataformaVirtual = async (req, res) => {
  const {
    tipo_infraestructura,
    nombre_identificacion,
    comuna,
    direccion_inmueble,
    sistema_gestion_aprendizajes,
    sistema_video_conferencia,
    sistema_aplicacion_evaluacion,
    descripcion_plataforma_virtual,
    vigencia,
    anio_proceso,
    creador_email,
    creador_id,
    userToken,
  } = req.body;

  // List of required attributes for plataformaVirtual
  const requiredAttributes = [
    "tipo_infraestructura",
    "nombre_identificacion",
    "comuna",
    "direccion_inmueble",
    "sistema_gestion_aprendizajes",
    "sistema_video_conferencia",
    "sistema_aplicacion_evaluacion",
    "descripcion_plataforma_virtual",
    "vigencia",
    "anio_proceso",
    "creador_email",
    "userToken",
  ];

  try {
    // Check the database connection
    await checkDatabase();
    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredAttributes
    );
    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }
    if (vigencia != 0 && vigencia != 1) {
      return res.status(400).json({ message: "Invalid vigencia value" });
    }
    const validateString = validateStringsAZ([
      sistema_gestion_aprendizajes,
      sistema_video_conferencia,
      sistema_aplicacion_evaluacion,
      descripcion_plataforma_virtual,
    ]);
    if (!validateString) {
      return res.status(400).json({
        message: "Invalid string in fields, only from a to Z accepted",
      });
    }
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }

    // Validate that creador_email exists in the Usuario model and matches the creador_id (case-insensitive using Op.iLike)
    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: creador_email },
      },
    });

    if (!usuario) {
      return res.status(404).json({
        message:
          "User not found or does not have access to the specified resource",
      });
    }

    // Validate the user's roles
    const validRoles = [
      "Encargado",
      "Usuario General",
      "Administrador Sistema",
    ];

    const hasAccess = await checkPermisosUsuario(
      creador_email,
      validRoles,
      "OTI"
    );
    if (!hasAccess) {
      return res.status(401).json({
        message:
          "Unauthorized access: User does not have the required permissions",
      });
    }

    // Validate that creador_email exists in the Usuario model and matches the creador_id (case-insensitive using Op.iLike)
    const infraestructuraRecurso = await InfraestructuraRecurso.findOne({
      where: { anio_proceso: anio_proceso },
    });

    if (!infraestructuraRecurso) {
      return res.status(404).json({
        message: "infraestructuraRecurso not found in Usuario model",
      });
    }
    // If the infarestructuraRecurso is already finalized, then the plataformaVirtual can't be created
    if (infraestructuraRecurso.is_finalized == true) {
      return res.status(403).json({
        message:
          "Can't create new plataformaVirtual if InfraestructuraRecurso is already validated",
      });
    }

    // Create the new plataformaVirtual record
    const newPlataforma = await PlataformaVirtual.create({
      tipo_infraestructura: 6,
      nombre_identificacion,
      comuna,
      direccion_inmueble,
      sistema_gestion_aprendizajes,
      sistema_video_conferencia,
      sistema_aplicacion_evaluacion,
      descripcion_plataforma_virtual,
      vigencia,
      anio_proceso,
      creador_email,
      creador_id: usuario.usuario_id,
      infraestructuraRecurso_id:
        infraestructuraRecurso.infraestructuraRecurso_id,
    });
    res.status(201).json({
      message: "PlataformaVirtual created successfully",
      newPlataforma,
    });
  } catch (error) {
    console.error("Error creating plataformaVirtual:", error);
    res.status(500).json({ message: "Error creating plataformaVirtual" });
  }
};

/**
 * Edit a specific plataformaVirtual by plataforma_id.
 */
export const editPlataformaVirtual = async (req, res) => {
  const { plataforma_id } = req.params;
  var {
    tipo_infraestructura,
    nombre_identificacion,
    comuna,
    direccion_inmueble,
    sistema_gestion_aprendizajes,
    sistema_video_conferencia,
    sistema_aplicacion_evaluacion,
    descripcion_plataforma_virtual,
    vigencia,
    anio_proceso,
    creador_email,
    creador_id,
    userToken,
    version_lock,
  } = req.body;

  // List of required attributes for plataformaVirtual
  const requiredAttributes = [
    "tipo_infraestructura",
    "nombre_identificacion",
    "comuna",
    "direccion_inmueble",
    "sistema_gestion_aprendizajes",
    "sistema_video_conferencia",
    "sistema_aplicacion_evaluacion",
    "descripcion_plataforma_virtual",
    "vigencia",
    "anio_proceso",
    "creador_email",
    "userToken",
    "version_lock",
  ];

  try {
    // Check the database connection
    await checkDatabase();
    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredAttributes
    );
    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }
    if (vigencia != 0 && vigencia != 1) {
      return res.status(400).json({ message: "Invalid vigencia value" });
    }

    const validateString = validateStringsAZ([
      sistema_gestion_aprendizajes,
      sistema_video_conferencia,
      sistema_aplicacion_evaluacion,
      descripcion_plataforma_virtual,
    ]);
    if (!validateString) {
      return res.status(400).json({
        message: "Invalid string in fields, only from a to Z accepted",
      });
    }

    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }

    // Validate that creador_email exists in the Usuario model and matches the creador_id (case-insensitive using Op.iLike)
    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: creador_email },
      },
    });

    if (!usuario) {
      return res.status(404).json({
        message:
          "User not found or does not have access to the specified resource",
      });
    }

    // Validate the user's roles
    const validRoles = [
      "Encargado",
      "Usuario General",
      "Administrador Sistema",
    ];

    const hasAccess = await checkPermisosUsuario(
      creador_email,
      validRoles,
      "OTI"
    );
    if (!hasAccess) {
      return res.status(401).json({
        message:
          "Unauthorized access: User does not have the required permissions",
      });
    }

    // Validate plataforma_id is a valid UUID
    if (!uuidValidate(plataforma_id)) {
      return res.status(400).json({ message: "Invalid plataforma_id" });
    }

    // Check if plataformaVirtual exists
    const plataforma = await PlataformaVirtual.findByPk(plataforma_id);
    if (!plataforma) {
      return res.status(404).json({ message: "PlataformaVirtual not found" });
    }

    const infraestructuraRecurso = await InfraestructuraRecurso.findByPk(
      plataforma.infraestructuraRecurso_id
    );
    if (!infraestructuraRecurso) {
      return res
        .status(404)
        .json({ message: "InfraestructuraRecurso not found" });
    }
    // If the infarestructuraRecurso is already finalized, then the plataformaVirtual can't be updated
    if (infraestructuraRecurso.is_finalized == true) {
      return res.status(403).json({
        message:
          "Can't update plataformaVirtual if infraestructuraRecurso is already validated",
      });
    }

    // Update the plataformaVirtual record
    tipo_infraestructura = 6;

    if (plataforma.version_lock != version_lock) {
      return res.status(409).json({
        message: "La plataforma virtual fue modificado por otro usuario",
      });
    }
    await plataforma.update({
      tipo_infraestructura,
      nombre_identificacion,
      comuna,
      direccion_inmueble,
      sistema_gestion_aprendizajes,
      sistema_video_conferencia,
      sistema_aplicacion_evaluacion,
      descripcion_plataforma_virtual,
      vigencia,
      anio_proceso,
      version_lock: version_lock + 1,
    });
    res
      .status(200)
      .json({ message: "PlataformaVirtual updated successfully", plataforma });
  } catch (error) {
    console.error("Error updating plataformaVirtual:", error);
    res.status(500).json({ message: "Error updating plataformaVirtual" });
  }
};

/**
 * Delete a specific plataformaVirtual by plataforma_id.
 */
export const deletePlataformaVirtual = async (req, res) => {
  const { plataforma_id } = req.params;
  const { userEmail, userToken } = req.body;

  try {
    // Check the database connection
    await checkDatabase();
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    // Validate that creador_email exists in the Usuario model (case-insensitive)
    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: userEmail },
      },
    });
    if (!usuario) {
      return res.status(404).json({
        message:
          "User not found or does not have access to the specified resource",
      });
    }

    // Validate the user's roles
    const validRoles = [
      "Encargado",
      "Usuario General",
      "Administrador Sistema",
    ];

    const hasAccess = await checkPermisosUsuario(
      userEmail,
      validRoles,
      "OTI"
    );
    if (!hasAccess) {
      return res.status(401).json({
        message:
          "Unauthorized access: User does not have the required permissions",
      });
    }

    if (!uuidValidate(plataforma_id)) {
      return res.status(400).json({ message: "Invalid plataforma_id" });
    }
    const plataforma = await PlataformaVirtual.findByPk(plataforma_id);
    if (!plataforma) {
      return res.status(404).json({ message: "PlataformaVirtual not found" });
    }
    const infraestructuraRecurso = await InfraestructuraRecurso.findOne({
      where: { anio_proceso: plataforma.anio_proceso },
    });
    // If the infarestructuraRecurso is already finalized, then the plataformaVirtual can't be deleted
    if (infraestructuraRecurso.is_finalized == true) {
      return res.status(403).json({
        message:
          "Can't delete plataformaVirtual if InfraestructuraRecurso is already validated",
      });
    }
    await plataforma.destroy();
    res.status(200).json({ message: "PlataformaVirtual deleted successfully" });
  } catch (error) {
    console.error("Error deleting plataformaVirtual:", error);
    res.status(500).json({ message: "Error deleting plataformaVirtual" });
  }
};
