// src/models/Mantenedores/lista_areas_institucionales.js
import { Model, DataTypes } from "sequelize";
import sequelize from "../../database/database.js";

class Lista_Areas_Institucionales extends Model {}

Lista_Areas_Institucionales.init(
  {
    area_institucional_id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    nombre_area_institucional: {
      type: DataTypes.STRING,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "Lista_Areas_Institucionales",
    freezeTableName: true,
  }
);

export default Lista_Areas_Institucionales;
