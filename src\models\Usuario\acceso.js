// src/models/Usuario/acceso.js
import { Model, DataTypes } from "sequelize";
import sequelize from "../../database/database.js";

class Acceso extends Model { }

Acceso.init({
    acceso_id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
        allowNull: false,
    },
    rol: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    recurso: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    accion: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    usuario_id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        allowNull: false,
    },
}, {
    sequelize,
    modelName: "Acceso",
    freezeTableName: true
});



export default Acceso;