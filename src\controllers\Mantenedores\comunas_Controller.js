import { checkAtributosNecesarios } from "../../utils/checkAtributosNecesarios.js";
import uuidValidate from "uuid-validate";
import Lista_Comunas_OTI from "../../models/Mantenedores/lista_comunas_oti.js";
import { checkTokenLocal, getEmailFromToken } from "../../auth/auth.js";
import { checkPermisosUsuario } from "../../utils/checkPermisosUsuario.js";

/**
 * Fetch all comuna associated with a specific comuna_id.
 */
export const getAllComunas = async (req, res) => {
  const userToken = req.headers.authorization;

  try {
    if (!userToken) {
      return res
        .status(401)
        .json({ message: "Authorization token is missing" });
    }
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    const creador_email = await getEmailFromToken(userToken);
    if (!creador_email) {
      return res.status(401).json({
        message: "Invalid token: Unable to retrieve email",
      });
    }
    // Validate the user's roles
    const validRoles = [
      "Encargado",
      "Administrador Sistema",
      "Usuario General",
    ];

    const hasAccess = await checkPermisosUsuario(
      creador_email,
      validRoles,
      "OTI"
    );
    if (!hasAccess) {
      return res.status(401).json({
        message:
          "Unauthorized access: User does not have the required permissions",
      });
    }

    const comuna_list = await Lista_Comunas_OTI.findAll();

    res.status(200).json(comuna_list);
  } catch (error) {
    console.error("Error fetching all Comuna:", error);
    res.status(500).json({ message: "Error fetching Comuna" });
  }
};

/**
 * Create a new comuna given a nombre_comuna
 */
export const postComuna = async (req, res) => {
  const { nombre_comuna } = req.body;
  const userToken = req.headers.authorization;

  // List of required attributes for comuna
  const requiredComunaFields = ["nombre_comuna"];

  try {
    if (!userToken) {
      return res
        .status(401)
        .json({ message: "Authorization token is missing" });
    }
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }

    const creador_email = await getEmailFromToken(userToken);
    if (!creador_email) {
      return res.status(401).json({
        message: "Invalid token: Unable to retrieve email",
      });
    }
    // Validate the user's roles
    const validRoles = ["Encargado", "Administrador Sistema"];

    const hasAccess = await checkPermisosUsuario(
      creador_email,
      validRoles,
      "OTI"
    );
    if (!hasAccess) {
      return res.status(401).json({
        message:
          "Unauthorized access: User does not have the required permissions",
      });
    }
    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredComunaFields
    );
    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }

    // Create the new comuna record
    const newComuna = await Lista_Comunas_OTI.create({
      nombre_comuna,
      creador_email,
    });
    res.status(201).json({
      message: "Comuna created successfully",
      newComuna,
    });
  } catch (error) {
    console.error("Error creating comuna:", error);
    res.status(500).json({ message: "Error creating comuna" });
  }
};

/**
 * Delete a specific comuna given it id.
 */
export const deleteComuna = async (req, res) => {
  const { comuna_id } = req.params;
  const userToken = req.headers.authorization;

  try {
    if (!uuidValidate(comuna_id)) {
      return res.status(400).json({ message: "Invalid comuna_id" });
    }
    if (!userToken) {
      return res
        .status(401)
        .json({ message: "Authorization token is missing" });
    }
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    const creador_email = await getEmailFromToken(userToken);
    if (!creador_email) {
      return res.status(401).json({
        message: "Invalid token: Unable to retrieve email",
      });
    }
    // Validate the user's roles
    const validRoles = ["Encargado", "Administrador Sistema"];

    const hasAccess = await checkPermisosUsuario(
      creador_email,
      validRoles,
      "OTI"
    );
    if (!hasAccess) {
      return res.status(401).json({
        message:
          "Unauthorized access: User does not have the required permissions",
      });
    }

    const comuna = await Lista_Comunas_OTI.findByPk(comuna_id);
    if (!comuna) {
      return res.status(404).json({ message: "Comuna not found" });
    }

    await comuna.destroy();
    res.status(200).json({ message: "Comuna deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: "Error deleting Comuna" });
  }
};
