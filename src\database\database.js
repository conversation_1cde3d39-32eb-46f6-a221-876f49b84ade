import Sequelize from "sequelize";
import dotenv from "dotenv";

dotenv.config();

const dbHost = process.env.DB_HOST;
const dbName = process.env.DB_NAME;
const dbUser = process.env.DB_USER;
const dbPassword = process.env.DB_PASSWORD;
const dbPort = process.env.DB_PORT;

async function initializeDatabase(retries = 100, delay = 5000) {
  while (retries) {
    try {
      // Initialize Sequelize instance
      const sequelize = new Sequelize(dbName, dbUser, dbPassword, {
        dialect: "postgres",
        host: dbHost,
        timezone: "America/Santiago",
        port: dbPort,
        retry: {
        max: 5, // Number of retry attempts
        match: [
            Sequelize.ConnectionError,
            Sequelize.ConnectionRefusedError,
            Sequelize.HostNotFoundError,
            Sequelize.HostNotReachableError,
            Sequelize.InvalidConnectionError,
        ],
    },
      });

      // Test connection
      await sequelize.authenticate();
      console.log("Database connected successfully.");
      return sequelize; // Exit loop if successful
    } catch (error) {
      console.error(`Database connection failed when starting the App. Retries left: ${retries - 1}`);
      retries--;
      if (!retries) {
        console.error("Exhausted all retries. Exiting...");
        process.exit(1); // Exit if retries are exhausted
      }

      // Wait for the specified delay before retrying
      await new Promise((res) => setTimeout(res, delay));
    }
  }
}

// Export the initialized Sequelize instance
export const sequelize = await initializeDatabase();
export default sequelize;
