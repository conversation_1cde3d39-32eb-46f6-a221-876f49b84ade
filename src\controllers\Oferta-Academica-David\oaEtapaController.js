import { Op } from "sequelize";
import OAEtapa from "../../models/Oferta-<PERSON><PERSON>-<PERSON>/OAEtapa.js";
import OASubProceso from "../../models/Oferta-<PERSON><PERSON>-<PERSON>/OASubProceso.js";
import OAProceso from "../../models/Oferta-Academic<PERSON>-<PERSON>/OAProceso.js";
import OAOfertaAcademica from "../../models/Oferta-Academica-David/O<PERSON>OfertaAcademica.js";
import { checkTokenLocal, getEmailFromToken } from "../../auth/auth.js";
import Usuario from "../../models/Usuario/usuario.js";
import { sequelize } from "../../database/database.js";
import { checkAtributosNecesarios } from "../../utils/checkAtributosNecesarios.js";
import { Sequelize } from "sequelize";
import Papa from "papaparse";

/**
 * Helper function to check database connection
 */
const checkDatabase = async () => {
  try {
    console.log("Checking database connection...");
    await sequelize.authenticate();
    console.log("Database is connected successfully.");
  } catch (error) {
    console.error("Database connection error:", error);
    throw new Error("Database not connected");
  }
};

/**
 * Get all OAEtapas
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getAllOAEtapas = async (req, res) => {
  try {
    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Fetch all etapas with their associated subproceso
    const etapas = await OAEtapa.findAll({
      include: [
        {
          model: OASubProceso,
          attributes: ['id', 'proceso_id', 'tipo', 'fecha_inicial', 'fecha_final']
        }
      ],
      order: [["id", "ASC"]]
    });

    res.status(200).json(etapas);
  } catch (error) {
    console.error("Error fetching all etapas:", error);
    res.status(500).json({ message: "Error fetching etapas" });
  }
};

/**
 * Get OAEtapas by subproceso_id with count of OAOfertaAcademicas
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getOAEtapasBySubProcesoId = async (req, res) => {
  try {
    const { subproceso_id } = req.params;
    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Fetch etapas by subproceso_id with count of OAOfertaAcademicas
    const etapas = await OAEtapa.findAll({
      where: { subproceso_id },
      attributes: {
        include: [
          [Sequelize.fn('COUNT', Sequelize.col('OAOfertaAcademicas.oa_id')), 'carrerasCount']
        ]
      },
      include: [
        {
          model: OASubProceso,
          attributes: ['id', 'proceso_id', 'tipo', 'fecha_inicial', 'fecha_final']
        },
        {
          model: OAOfertaAcademica,
          attributes: [],
          required: false // Left join to include etapas with no ofertas academicas
        }
      ],
      group: ['OAEtapa.id', 'OASubProceso.id'], // Group by to get count per etapa
      order: [["etapa", "ASC"]]
    });

    res.status(200).json({
      message: "Etapas with carreras count retrieved successfully",
      data: etapas
    });
  } catch (error) {
    console.error("Error fetching etapas by subproceso_id:", error);
    res.status(500).json({ message: "Error fetching etapas", error: error.message });
  }
};

/**
 * Get OAEtapa by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getOAEtapaById = async (req, res) => {
  try {
    const { id } = req.params;
    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Find etapa by ID
    const etapa = await OAEtapa.findByPk(id, {
      include: [
        {
          model: OASubProceso,
          attributes: ['id', 'proceso_id', 'tipo', 'fecha_inicial', 'fecha_final']
        }
      ]
    });

    if (!etapa) {
      return res.status(404).json({ message: "Etapa not found" });
    }

    res.status(200).json(etapa);
  } catch (error) {
    console.error("Error fetching etapa by ID:", error);
    res.status(500).json({ message: "Error fetching etapa" });
  }
};

/**
 * Create a new OAEtapa
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const createOAEtapa = async (req, res) => {
  try {
    const { subproceso_id, tipo, etapa, fecha_inicial, fecha_final, validado } = req.body;
    const userToken = req.headers.authorization;

    // Required fields
    const requiredFields = ["subproceso_id", "tipo", "etapa", "fecha_inicial", "fecha_final"];

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredFields
    );

    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }

    // Create new etapa
    const newEtapa = await OAEtapa.create({
      subproceso_id,
      tipo,
      etapa,
      fecha_inicial,
      fecha_final,
      validado: validado || false
    });

    res.status(201).json({
      message: "Etapa created successfully",
      etapa: newEtapa,
    });
  } catch (error) {
    console.error("Error creating etapa:", error);
    res.status(500).json({ message: "Error creating etapa" });
  }
};

/**
 * Update an existing OAEtapa
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const updateOAEtapa = async (req, res) => {
  try {
    const { id } = req.params;
    const { subproceso_id, tipo, etapa, fecha_inicial, fecha_final, validado } = req.body;
    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Find etapa by ID
    const etapaToUpdate = await OAEtapa.findByPk(id);

    if (!etapaToUpdate) {
      return res.status(404).json({ message: "Etapa not found" });
    }

    // Update etapa
    await etapaToUpdate.update({
      subproceso_id,
      tipo,
      etapa,
      fecha_inicial,
      fecha_final,
      validado
    });

    res.status(200).json({
      message: "Etapa updated successfully",
      etapa: etapaToUpdate,
    });
  } catch (error) {
    console.error("Error updating etapa:", error);
    res.status(500).json({ message: "Error updating etapa" });
  }
};

/**
 * Delete an OAEtapa
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const deleteOAEtapa = async (req, res) => {
  try {
    const { id } = req.params;
    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Find etapa by ID
    const etapa = await OAEtapa.findByPk(id);

    if (!etapa) {
      return res.status(404).json({ message: "Etapa not found" });
    }

    // Use a transaction to ensure all operations succeed or fail together
    const result = await sequelize.transaction(async (t) => {
      // First, delete all ofertas academicas associated with this etapa
      const deletedOfertasCount = await OAOfertaAcademica.destroy({
        where: { etapa_id: id },
        transaction: t
      });

      // Then delete the etapa
      await etapa.destroy({ transaction: t });

      return { deletedOfertasCount };
    });

    res.status(200).json({
      message: "Etapa deleted successfully",
      deletedOfertasCount: result.deletedOfertasCount
    });
  } catch (error) {
    console.error("Error deleting etapa:", error);
    res.status(500).json({
      message: "Error deleting etapa",
      error: error.message
    });
  }
};

/**
 * Create a new OAEtapa with OAOfertaAcademicas from CSV
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
// export const createEtapaWithOfertasFromCSV = async (req, res) => {
//   try {
//     const { subproceso_id, tipo, etapa, fecha_termino } = req.body;
//     const { ofertas_academicas } = req.body; // CSV data in JSON format
//     const userToken = req.headers.authorization;

//     // Required fields
//     const requiredFields = ["subproceso_id", "tipo", "etapa", "fecha_termino", "ofertas_academicas"];

//     // Check database connection
//     await checkDatabase();

//     // Validate token
//     if (!(await checkTokenLocal(userToken))) {
//       return res.status(401).json({
//         message: "Token is not valid",
//       });
//     }

//     // Check for missing required attributes
//     const missingAttributes = checkAtributosNecesarios(
//       req.body,
//       requiredFields
//     );

//     if (missingAttributes.length > 0) {
//       return res.status(400).json({
//         message: `Missing required attributes: ${missingAttributes.join(", ")}`,
//       });
//     }

//     // Validate ofertas_academicas data
//     if (!Array.isArray(ofertas_academicas) || ofertas_academicas.length === 0) {
//       return res.status(400).json({
//         message: "ofertas_academicas must be a non-empty array",
//       });
//     }

//     // Get the current date for fecha_inicial
//     const fecha_inicial = new Date();

//     // Parse fecha_termino if it's a string
//     const parsedFechaTermino = typeof fecha_termino === 'string' ? new Date(fecha_termino) : fecha_termino;

//     // Create transaction to ensure all operations succeed or fail together
//     const result = await sequelize.transaction(async (t) => {
//       // Create new etapa
//       const newEtapa = await OAEtapa.create({
//         subproceso_id,
//         tipo,
//         etapa,
//         fecha_inicial,
//         fecha_final: parsedFechaTermino,
//         validado: false
//       }, { transaction: t });

//       // Normalize column names (convert to lowercase)
//       const normalizedOfertas = ofertas_academicas.map(oferta => {
//         const normalizedOferta = {};
//         for (const key in oferta) {
//           normalizedOferta[key.toLowerCase()] = oferta[key];
//         }
//         return normalizedOferta;
//       });

//       // Validate ofertas data
//       const validationErrors = validateOfertasAcademicas(normalizedOfertas);
//       if (validationErrors.length > 0) {
//         throw new Error(JSON.stringify(validationErrors));
//       }

//       // Create ofertas academicas with the new etapa_id
//       const createdOfertas = [];
//       for (const oferta of normalizedOfertas) {
//         const newOferta = await OAOfertaAcademica.create({
//           etapa_id: newEtapa.id,
//           cod_sede: oferta.cod_sede,
//           nombre_sede: oferta.nombre_sede,
//           cod_carrera: oferta.cod_carrera,
//           nombre_carrera: oferta.nombre_carrera,
//           modalidad: oferta.modalidad,
//           cod_jornada: oferta.cod_jornada,
//           version: oferta.version,
//           cod_tipo_plan_carrera: oferta.cod_tipo_plan_carrera,
//           caracteristicas_tipo_plan: oferta.caracteristicas_tipo_plan,
//           duracion_estudios: oferta.duracion_estudios,
//           duracion_titulacion: oferta.duracion_titulacion,
//           duracion_total: oferta.duracion_total,
//           regimen: oferta.regimen,
//           duracion_regimen: oferta.duracion_regimen,
//           nombre_titulo: oferta.nombre_titulo,
//           nombre_grado: oferta.nombre_grado,
//           cod_nivel_global: oferta.cod_nivel_global,
//           cod_nivel_carrera: oferta.cod_nivel_carrera,
//           cod_demre: oferta.cod_demre,
//           anio_inicio: oferta.anio_inicio,
//           acreditacion: oferta.acreditacion,
//           elegible_beca_pedagogia: oferta.elegible_beca_pedagogia,
//           ped_med_odont_otro: oferta.ped_med_odont_otro,
//           requisito_ingreso: oferta.requisito_ingreso,
//           semestres_reconocidos: oferta.semestres_reconocidos,
//           area_actual: oferta.area_actual,
//           area_destino_agricultura: oferta.area_destino_agricultura || false,
//           area_destino_ciencias: oferta.area_destino_ciencias || false,
//           area_destino_cs_sociales: oferta.area_destino_cs_sociales || false,
//           area_destino_educacion: oferta.area_destino_educacion || false,
//           area_destino_humanidades: oferta.area_destino_humanidades || false,
//           area_destino_ingenieria: oferta.area_destino_ingenieria || false,
//           area_destino_salud: oferta.area_destino_salud || false,
//           area_destino_servicios: oferta.area_destino_servicios || false,
//           ponderacion_nem: oferta.ponderacion_nem,
//           ponderacion_ranking: oferta.ponderacion_ranking,
//           ponderacion_c_lectora: oferta.ponderacion_c_lectora,
//           ponderacion_matematica_1: oferta.ponderacion_matematica_1,
//           ponderacion_matematica_2: oferta.ponderacion_matematica_2,
//           ponderacion_historia: oferta.ponderacion_historia,
//           ponderacion_ciencias: oferta.ponderacion_ciencias,
//           ponderacion_otros: oferta.ponderacion_otros,
//           vacantes_primer_semestre: oferta.vacantes_primer_semestre,
//           vacantes_segundo_semestre: oferta.vacantes_segundo_semestre,
//           vacantes_pace: oferta.vacantes_pace,
//           malla_curricular: oferta.malla_curricular,
//           perfil_egreso: oferta.perfil_egreso,
//           texto_requisito_ingreso: oferta.texto_requisito_ingreso,
//           otros_requisitos: oferta.otros_requisitos,
//           mail_difusion_carrera: oferta.mail_difusion_carrera,
//           vigencia_carrera: oferta.vigencia_carrera
//         }, { transaction: t });

//         createdOfertas.push(newOferta);
//       }

//       return {
//         etapa: newEtapa,
//         ofertasCount: createdOfertas.length
//       };
//     });

//     res.status(201).json({
//       message: "Etapa with ofertas academicas created successfully",
//       etapa: result.etapa,
//       ofertasCount: result.ofertasCount
//     });
//   } catch (error) {
//     console.error("Error creating etapa with ofertas academicas:", error);

//     // Check if error is from validation
//     if (error.message.startsWith('[') && error.message.endsWith(']')) {
//       try {
//         const validationErrors = JSON.parse(error.message);
//         return res.status(400).json({
//           message: "Validation errors in ofertas academicas data",
//           errors: validationErrors
//         });
//       } catch (e) {
//         // If parsing fails, return the original error
//       }
//     }

//     res.status(500).json({
//       message: "Error creating etapa with ofertas academicas",
//       error: error.message
//     });
//   }
// };


/**
 * Process CSV file for OAOfertaAcademicas
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const processCSVForOfertasAcademicas = async (req, res) => {
  try {
    const { csvData } = req.body;
    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    if (!csvData) {
      return res.status(400).json({
        message: "CSV data is required",
      });
    }

    // Parse CSV data
    const parsedData = Papa.parse(csvData, {
      header: true,
      skipEmptyLines: true,
    });

    if (parsedData.errors.length > 0) {
      return res.status(400).json({
        message: "Error parsing CSV data",
        errors: parsedData.errors,
      });
    }

    // Return the parsed data
    res.status(200).json({
      message: "CSV data processed successfully",
      data: parsedData.data,
      count: parsedData.data.length,
    });
  } catch (error) {
    console.error("Error processing CSV data:", error);
    res.status(500).json({
      message: "Error processing CSV data",
      error: error.message,
    });
  }
};

/**
 * Validate an OAEtapa
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const validateOAEtapa = async (req, res) => {
  try {
    const { id } = req.params;
    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Find etapa by ID
    const etapaToValidate = await OAEtapa.findByPk(id);

    if (!etapaToValidate) {
      return res.status(404).json({ message: "Etapa not found" });
    }

    // Update validado to true
    await etapaToValidate.update({ validado: true });
    // Find the associated subproceso
const subproceso = await OASubProceso.findByPk(etapaToValidate.subproceso_id);
if (subproceso) {
  const updates = {};

  switch (etapaToValidate.etapa) {
    case 1:
      updates.etapa_actual = 2;
      break;

    case 2:
      if (subproceso.tipo === 2) {
        updates.etapa_actual = 3;
      } else {
        updates.validado = true;
      }
      break;

    case 3:
      if (subproceso.tipo === 2) {
        updates.validado = true;
      }
      break;
  }

  if (Object.keys(updates).length > 0) {
    await subproceso.update(updates);
  }
}

    res.status(200).json({
      message: "Etapa validated successfully",
      etapa: etapaToValidate,
    });
  } catch (error) {
    console.error("Error validating etapa:", error);
    res.status(500).json({ message: "Error validating etapa" });
  }
};



