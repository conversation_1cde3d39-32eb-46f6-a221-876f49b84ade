
const rulesPregrado = [
  {
    nombre_campo: "ACREDITACION",
    etapa_actual: "1",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-2]$",
  },
  {
    nombre_campo: "ELEGIBLE_BECA_PEDAGOGIA",
    etapa_actual: "1",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0-3]$",
  },
  {
    nombre_campo: "REQUISITO_INGRESO",
    etapa_actual: "1",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^(10|[1-9])$",
  },
  {
    nombre_campo: "SEMESTRES_RECONOCIDOS",
    etapa_actual: "1",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "AREA_DESTINO_AGRICULTURA",
    etapa_actual: "1",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0-1]$",
  },
  {
    nombre_campo: "AREA_DESTINO_CIENCIAS",
    etapa_actual: "1",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0-1]$",
  },
  {
    nombre_campo: "AREA_DESTINO_CS_SOCIALES",
    etapa_actual: "1",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0-1]$",
  },
  {
    nombre_campo: "AREA_DESTINO_EDUCACION",
    etapa_actual: "1",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0-1]$",
  },
  {
    nombre_campo: "AREA_DESTINO_HUMANIDADES",
    etapa_actual: "1",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0-1]$",
  },
  {
    nombre_campo: "AREA_DESTINO_INGENIERIA",
    etapa_actual: "1",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0-1]$",
  },
  {
    nombre_campo: "AREA_DESTINO_SALUD",
    etapa_actual: "1",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0-1]$",
  },
  {
    nombre_campo: "AREA_DESTINO_SERVICIOS",
    etapa_actual: "1",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0-1]$",
  },
  {
    nombre_campo: "MALLA_CURRICULAR",
    etapa_actual: "1",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "alfanumerico",
    regex: "^[a-zA-Z0-9._~:/?#\\[\\]@!$&'()*+,;=%-]+$",
  },
  {
    nombre_campo: "MAIL_DIFUSION_CARRERA",
    etapa_actual: "1",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "alfanumerico",
    regex: "^[a-zA-Z0-9._~:/?#\\[\\]@!$&'()*+,;=%-]+$",
  },
];
const rulesPosgrado = [
  {
    nombre_campo: "REGIMEN",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-4]$",
  },
  {
    nombre_campo: "DURACION_REGIMEN",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "COD_DEMRE",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "ACREDITACION",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-2]$",
  },
  {
    nombre_campo: "ELEGIBLE_BECA_PEDAGOGIA",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0-3]$",
  },
  {
    nombre_campo: "REQUISITO_INGRESO",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^(10|[1-9])$",
  },
  {
    nombre_campo: "SEMESTRES_RECONOCIDOS",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "AREA_DESTINO_AGRICULTURA",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "AREA_DESTINO_CIENCIAS",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "AREA_DESTINO_CS_SOCIALES",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "AREA_DESTINO_EDUCACION",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "AREA_DESTINO_HUMANIDADES",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "AREA_DESTINO_INGENIERIA",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "AREA_DESTINO_SALUD",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "AREA_DESTINO_SERVICIOS",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "PONDERACION_NEM",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "PONDERACION_RANKING",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "PONDERACION_C_LECTORA",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "PONDERACION_MATEMATICAS_1",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "PONDERACION_MATEMATICAS_2",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "PONDERACION_HISTORIA",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "PONDERACION_CIENCIAS",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "PONDERACION_OTROS",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "VACANTES_PRIMER_SEMESTRE",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "VACANTES_SEGUNDO_SEMESTRE",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "VACANTES_PACE",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "vacio",
    regex: "^$",
  },
  {
    nombre_campo: "MALLA_CURRICULAR",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "vacio",
    regex: "^$",
  },
  {
    nombre_campo: "PERFIL_EGRESO",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "vacio",
    regex: "^$",
  },
  {
    nombre_campo: "TEXTO_REQUISITO_INGRESO",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "vacio",
    regex: "^$",
  },
  {
    nombre_campo: "OTROS_REQUISITOS",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "vacio",
    regex: "^$",
  },
  {
    nombre_campo: "MAIL_DIFUSION_CARRERA",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "vacio",
    regex: "^$",
  },
  {
    nombre_campo: "VIGENCIA_CARRERA",
    etapa_actual: "1",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-3]$",
  },
];
const rulesPostitulo = [
  {
    nombre_campo: "REGIMEN",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-4]$",
  },
  {
    nombre_campo: "DURACION_REGIMEN",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "COD_DEMRE",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "ACREDITACION",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-2]$",
  },
  {
    nombre_campo: "ELEGIBLE_BECA_PEDAGOGIA",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0-3]$",
  },
  {
    nombre_campo: "REQUISITO_INGRESO",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^(10|[1-9])$",
  },
  {
    nombre_campo: "SEMESTRES_RECONOCIDOS",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "AREA_DESTINO_AGRICULTURA",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "AREA_DESTINO_CIENCIAS",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "AREA_DESTINO_CS_SOCIALES",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "AREA_DESTINO_EDUCACION",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "AREA_DESTINO_HUMANIDADES",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "AREA_DESTINO_INGENIERIA",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "AREA_DESTINO_SALUD",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "AREA_DESTINO_SERVICIOS",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "PONDERACION_NEM",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "PONDERACION_RANKING",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "PONDERACION_C_LECTORA",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "PONDERACION_MATEMATICAS_1",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "PONDERACION_MATEMATICAS_2",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "PONDERACION_HISTORIA",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "PONDERACION_CIENCIAS",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "PONDERACION_OTROS",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "numero",
    regex: "^[0]$",
  },
  {
    nombre_campo: "VACANTES_PRIMER_SEMESTRE",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "VACANTES_SEGUNDO_SEMESTRE",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "VACANTES_PACE",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "vacio",
    regex: "^$",
  },
  {
    nombre_campo: "MALLA_CURRICULAR",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "vacio",
    regex: "^$",
  },
  {
    nombre_campo: "PERFIL_EGRESO",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "vacio",
    regex: "^$",
  },
  {
    nombre_campo: "TEXTO_REQUISITO_INGRESO",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "vacio",
    regex: "^$",
  },
  {
    nombre_campo: "OTROS_REQUISITOS",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: false,
    especificaciones: "vacio",
    regex: "^$",
  },
  {
    nombre_campo: "MAIL_DIFUSION_CARRERA",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "vacio",
    regex: "^$",
  },
  {
    nombre_campo: "VIGENCIA_CARRERA",
    etapa_actual: "1",
    tipo_acceso: "postitulo",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-3]$",
  },
];

export { rulesPregrado, rulesPosgrado, rulesPostitulo };
