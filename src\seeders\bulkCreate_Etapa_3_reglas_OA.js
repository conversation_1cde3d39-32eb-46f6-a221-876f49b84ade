const rulesPregrado3 = [
  {
    nombre_campo: "ACREDITACION",
    etapa_actual: "3",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-2]$",
  },
  {
    nombre_campo: "MALLA_CURRICULAR",
    etapa_actual: "3",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "alfanumerico",
    regex: "^[a-zA-Z0-9._~:/?#\\[\\]@!$&'()*+,;=%-]+$",
  },
  {
    nombre_campo: "MAIL_DIFUSION_CARRERA",
    etapa_actual: "3",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "alfanumerico",
    regex: "^[a-zA-Z0-9._~:/?#\\[\\]@!$&'()*+,;=%-]+$",
  },
  {
    nombre_campo: "FORMATO_VALOR",
    etapa_actual: "3",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-2]$",
  },
  {
    nombre_campo: "VALOR_MATRICULA_ANUAL",
    etapa_actual: "3",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "decimal",
    regex: "^\\d+(,\\d)?$",
  },
  {
    nombre_campo: "COSTO_TITULACION",
    etapa_actual: "3",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "decimal",
    regex: "^\\d+(,\\d)?$",
  },
  {
    nombre_campo: "VALOR_CERTIFICADO_DIPLOMA",
    etapa_actual: "3",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "decimal",
    regex: "^\\d+(,\\d)?$",
  },
  {
    nombre_campo: "ARANCEL_ANUAL",
    etapa_actual: "3",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "decimal",
    regex: "^\\d+(,\\d)?$",
  },
  {
    nombre_campo: "VIGENCIA_CARRERA",
    etapa_actual: "3",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-2]$",
  },
];
const rulesPosgrado3 = [
  {
    nombre_campo: "ACREDITACION",
    etapa_actual: "3",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-2]$",
  },
  {
    nombre_campo: "MALLA_CURRICULAR",
    etapa_actual: "3",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "vacio",
    regex: "^$",
  },
  {
    nombre_campo: "MAIL_DIFUSION_CARRERA",
    etapa_actual: "3",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "vacio",
    regex: "^$",
  },
  {
    nombre_campo: "FORMATO_VALOR",
    etapa_actual: "3",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-2]$",
  },
  {
    nombre_campo: "VALOR_MATRICULA_ANUAL",
    etapa_actual: "3",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "decimal",
    regex: "^\\d+(,\\d)?$",
  },
  {
    nombre_campo: "COSTO_TITULACION",
    etapa_actual: "3",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "decimal",
    regex: "^\\d+(,\\d)?$",
  },
  {
    nombre_campo: "VALOR_CERTIFICADO_DIPLOMA",
    etapa_actual: "3",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "decimal",
    regex: "^\\d+(,\\d)?$",
  },
  {
    nombre_campo: "ARANCEL_ANUAL",
    etapa_actual: "3",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "decimal",
    regex: "^\\d+(,\\d)?$",
  },
  {
    nombre_campo: "VIGENCIA_CARRERA",
    etapa_actual: "3",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-2]$",
  },
];
const rulesPostitulo3 = [
    {
        nombre_campo: "ACREDITACION",
        etapa_actual: "3",
        tipo_acceso: "posgrado",
        es_demre: true,
        obligatorio: true,
        permite_cambios: true,
        especificaciones: "numero",
        regex: "^[1-2]$",
      },
      {
        nombre_campo: "MALLA_CURRICULAR",
        etapa_actual: "3",
        tipo_acceso: "posgrado",
        es_demre: true,
        obligatorio: true,
        permite_cambios: true,
        especificaciones: "vacio",
        regex: "^$",
      },
      {
        nombre_campo: "MAIL_DIFUSION_CARRERA",
        etapa_actual: "3",
        tipo_acceso: "posgrado",
        es_demre: true,
        obligatorio: true,
        permite_cambios: true,
        especificaciones: "vacio",
        regex: "^$",
      },
      {
        nombre_campo: "FORMATO_VALOR",
        etapa_actual: "3",
        tipo_acceso: "postitulo",
        es_demre: true,
        obligatorio: true,
        permite_cambios: true,
        especificaciones: "numero",
        regex: "^[1-2]$",
      },
      {
        nombre_campo: "VALOR_MATRICULA_ANUAL",
        etapa_actual: "3",
        tipo_acceso: "postitulo",
        es_demre: true,
        obligatorio: true,
        permite_cambios: true,
        especificaciones: "decimal",
        regex: "^\\d+(,\\d)?$",
      },
      {
        nombre_campo: "COSTO_TITULACION",
        etapa_actual: "3",
        tipo_acceso: "postitulo",
        es_demre: true,
        obligatorio: true,
        permite_cambios: true,
        especificaciones: "decimal",
        regex: "^\\d+(,\\d)?$",
      },
      {
        nombre_campo: "VALOR_CERTIFICADO_DIPLOMA",
        etapa_actual: "3",
        tipo_acceso: "postitulo",
        es_demre: true,
        obligatorio: true,
        permite_cambios: true,
        especificaciones: "decimal",
        regex: "^\\d+(,\\d)?$",
      },
      {
        nombre_campo: "ARANCEL_ANUAL",
        etapa_actual: "3",
        tipo_acceso: "postitulo",
        es_demre: true,
        obligatorio: true,
        permite_cambios: true,
        especificaciones: "decimal",
        regex: "^\\d+(,\\d)?$",
      },
      {
        nombre_campo: "VIGENCIA_CARRERA",
        etapa_actual: "3",
        tipo_acceso: "postitulo",
        es_demre: true,
        obligatorio: true,
        permite_cambios: true,
        especificaciones: "numero",
        regex: "^[1-2]$",
      },
];

export { rulesPregrado3, rulesPosgrado3, rulesPostitulo3 };
