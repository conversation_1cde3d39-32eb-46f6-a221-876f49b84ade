import { Model, DataTypes } from "sequelize";
import sequelize from "../../database/database.js";
import inmueblePermanente from "./inmueblePermanente.js";
import InmuebleRestringido from "./inmuebleRestringido.js";
import Biblioteca from "./biblioteca.js";
import LibrosBasesDigitales from "./librosBasesDigitales.js";
import Predio from "./predio.js";
import PlataformaVirtual from "./plataformaVirtual.js";

class InfraestructuraRecurso extends Model {}

InfraestructuraRecurso.init(
  {
    infraestructuraRecurso_id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    anio_proceso: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: true,
    },
    is_finalized: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
    },
    validated_by: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    creador_email: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isEmail: true, // Ensures the email format is valid
      },
    },
    creador_id: {
      type: DataTypes.UUID,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "InfraestructuraRecurso",
    freezeTableName: true,
  }
);

// One-to-Many relationship with "inmueblePermanente"
InfraestructuraRecurso.hasMany(inmueblePermanente, {
  foreignKey: "infraestructuraRecurso_id",
  onDelete: "RESTRICT",
});
inmueblePermanente.belongsTo(InfraestructuraRecurso, {
  foreignKey: "infraestructuraRecurso_id",
  onDelete: "RESTRICT",
});

// One-to-Many relationship with "inmuebleRestringido"
InfraestructuraRecurso.hasMany(InmuebleRestringido, {
  foreignKey: "infraestructuraRecurso_id",
  onDelete: "RESTRICT",
});
InmuebleRestringido.belongsTo(InfraestructuraRecurso, {
  foreignKey: "infraestructuraRecurso_id",
  onDelete: "RESTRICT",
});

// One-to-Many relationship with "biblioteca"
InfraestructuraRecurso.hasMany(Biblioteca, {
  foreignKey: "infraestructuraRecurso_id",
  onDelete: "RESTRICT",
});
Biblioteca.belongsTo(InfraestructuraRecurso, {
  foreignKey: "infraestructuraRecurso_id",
  onDelete: "RESTRICT",
});

// One-to-Many relationship with "librosBasesDigitales"
InfraestructuraRecurso.hasMany(LibrosBasesDigitales, {
  foreignKey: "infraestructuraRecurso_id",
  onDelete: "RESTRICT",
});
LibrosBasesDigitales.belongsTo(InfraestructuraRecurso, {
  foreignKey: "infraestructuraRecurso_id",
  onDelete: "RESTRICT",
});

// One-to-Many relationship with "predio"
InfraestructuraRecurso.hasMany(Predio, {
  foreignKey: "infraestructuraRecurso_id",
  onDelete: "RESTRICT",
});
Predio.belongsTo(InfraestructuraRecurso, {
  foreignKey: "infraestructuraRecurso_id",
  onDelete: "RESTRICT",
});

// One-to-Many relationship with "PlataformaVirtual"
InfraestructuraRecurso.hasMany(PlataformaVirtual, {
  foreignKey: "infraestructuraRecurso_id",
  onDelete: "RESTRICT",
});
PlataformaVirtual.belongsTo(InfraestructuraRecurso, {
  foreignKey: "infraestructuraRecurso_id",
  onDelete: "RESTRICT",
});

export default InfraestructuraRecurso;
