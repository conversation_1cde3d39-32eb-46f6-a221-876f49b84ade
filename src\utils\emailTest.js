import dotenv from 'dotenv';
import nodemailer from 'nodemailer';

dotenv.config();

// Create a nodemailer transporter
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: process.env.EMAIL_USERNAME, // Email
    pass: process.env.EMAIL_PASSWORD, // Email password
  },
});

// Function to send email
export const sendTestEmail = async (to, subject, text) => {
  const mailOptions = {
    from: process.env.EMAIL_USERNAME, // Sender email
    to, // List of recipients
    subject, // Subject of the email
    text, // Plain text body
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log('Email sent: ' + info.response);
    return info;
  } catch (error) {
    console.error('Error occurred: ' + error.message);
    throw error;
  }
};
