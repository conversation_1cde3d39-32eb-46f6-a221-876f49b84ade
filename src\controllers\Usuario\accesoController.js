import Acceso from "../../models/Usuario/acceso.js";
import Usuario from "../../models/Usuario/usuario.js";
import { checkAtributosNecesarios } from "../../utils/checkAtributosNecesarios.js";
import { Op } from "sequelize";
import uuidValidate from "uuid-validate";
import { checkTokenLocal, getEmailFromToken } from "../../auth/auth.js";
import e from "express";

/**
 * Fetch all accesos associated with a specific usuario_id
 */
/*
export const getAllAccesos = async (req, res) => {
  const { usuario_id } = req.params;

  try {
    if (!uuidValidate(usuario_id)) {
      return res.status(400).json({ message: "Invalid usuario_id" });
    }

    const usuario = await Usuario.findByPk(usuario_id);
    if (!usuario) {
      return res.status(404).json({ message: "Usuario not found" });
    }

    const accesos = await Acceso.findAll({ where: { usuario_id } });

    if (accesos.length === 0) {
      return res.status(404).json({ message: "No accesos found" });
    }

    res.status(200).json(accesos);
  } catch (error) {
    console.error("Error fetching all accesos:", error);
    res.status(500).json({ message: "Error fetching accesos" });
  }
};*/

/**
 * Create a new acceso
 */
export const postAcceso = async (req, res) => {
  const { email, recurso, rol, accion } = req.body;
  const userToken = req.headers.authorization;
  const requiredAttributes = ["email", "recurso", "rol", "accion", ,];

  try {
    if (!userToken) {
      return res
        .status(401)
        .json({ message: "Unauthorized: No token provided" });
    }
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredAttributes
    );
    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }

    // check if the token is valid and not expired
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }

    // Get the email from the token
    const creador_email = await getEmailFromToken(userToken);

    // Get the data of the current user
    const currentUser = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: creador_email },
      },
      include: {
        model: Acceso,
        as: "accesos",
      },
    });
    if (!currentUser) {
      //user not found
      return res.status(404).json({
        message: "Current user not found",
      });
    }
    // Check if the current user has admin access
    const isAdmin = currentUser?.accesos.some(
      (acceso) => acceso.rol === "Administrador Sistema"
    );

    if (isAdmin) {
    } else {
      // Check if the current user has access to the resource
      const hasAccess = currentUser?.accesos.some(
        (acceso) => acceso.rol === "Encargado" && acceso.recurso === recurso
      );
      if (hasAccess) {
      } else {
        return res
          .status(401)
          .json({ message: "You dont have permission to create a new user" });
      }
    }

    const usuario = await Usuario.findOne({
      where: { email: { [Op.iLike]: email } },
    });

    if (!usuario) {
      return res.status(404).json({
        message: "Email not found in Usuario model",
      });
    }

    const newAcceso = await Acceso.create({
      usuario_id: usuario.usuario_id,
      recurso,
      rol,
      accion,
    });
    if (!newAcceso) {
      res.status(400).json({ message: "Error creting the acceso" });
    }

    res.status(201).json({ message: "Acceso created successfully", newAcceso });
  } catch (error) {
    console.error("Error creating acceso:", error);
    res.status(500).json({ message: "Error creating acceso" });
  }
};

export const deleteAcceso = async (req, res) => {
  const { email, rol, recurso } = req.body;
  const userToken = req.headers.authorization;
  try {
    if (!userToken) {
      return res
        .status(401)
        .json({ message: "Unauthorized: No token provided" });
    }
    // List of required attributes for Recurso
    const requiredrolFields = ["email", "rol", "recurso"];
    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredrolFields
    );
    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }

    // check if the token is valid and not expired
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }

    const selectedUser = await Usuario.findOne({
      where: {
        email: {
          [Op.iLike]: email,
        },
      },
    });
    if (!selectedUser) {
      return res.status(404).json({
        message: "User dont found",
      });
    }
    const selectedUserAccess = await Acceso.findOne({
      where: {
        usuario_id: selectedUser.usuario_id,
        rol: {
          [Op.iLike]: rol,
        },
        recurso: {
          [Op.iLike]: recurso,
        },
      },
    });
    if (!selectedUserAccess) {
      return res.status(404).json({
        message: "User dont have this access",
      });
    }

    const deletedAcceso = await selectedUserAccess.destroy();
    res.status(200).json({ message: "Acceso deleted successfully" });
  } catch (error) {
    console.error("Error deleting acceso:", error);
    res.status(500).json({ message: "Error deleting acceso" });
  }
};
