import { checkAtributosNecesarios } from "../../utils/checkAtributosNecesarios.js";
import uuidValidate from "uuid-validate";
import Lista_Roles from "../../models/Mantenedores/lista_roles.js";
import Acceso from "../../models/Usuario/acceso.js";
import Usuario from "../../models/Usuario/usuario.js";
import { checkTokenLocal, getEmailFromToken } from "../../auth/auth.js";
import { Op } from "sequelize";
/**
 * Fetch all Roles that the user has.
 */
export const getAllRoles = async (req, res) => {
  try {
    const userToken = req.headers.authorization;
    console.log(userToken);
    // Check if the token is present
    if (!userToken) {
      return res.status(400).json({ message: "Missing authorization token" });
    }

    // Check if the token is valid
    const isValidToken = checkTokenLocal(userToken);
    if (!isValidToken) {
      return res.status(401).json({ message: "Invalid token" });
    }

    // Get the email from the token
    const email = await getEmailFromToken(userToken);

    // Validate that "email" exists in the Usuario model (case-insensitive)
    if (!email) {
      return res.status(401).json({ message: "Invalid token" });
    }
    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: email },
      },
      include: {
        model: Acceso,
        as: "accesos",
        where: {
          [Op.or]: [{ rol: "Administrador Sistema" }],
        },
      },
    });
    var rol = [];
    if (!usuario) {
      // User is not "Administrador Sistema"
      rol = await Lista_Roles.findAll({
        where: {
          nombre_rol: {
            [Op.ne]: "Administrador Sistema",
          },
        },
      });
    } else {
      rol = await Lista_Roles.findAll();
    }
    if (rol.length === 0) {
      return res.status(404).json({ message: "No recurso found" });
    }

    res.status(200).json(rol);
  } catch (error) {
    console.error("Error fetching all rol:", error);
    res.status(500).json({ message: "Error fetching rol" });
  }
};

/**
 * Create a new rol given a nombre_rol
 */
export const postRol = async (req, res) => {
  const { nombre_rol } = req.body;

  // List of required attributes for Recurso
  const requiredrolFields = ["nombre_rol"];

  try {
    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredrolFields
    );
    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }
    // Create the new recurso record
    const newRol = await Lista_Roles.create({
      nombre_rol,
    });
    res.status(201).json({
      message: "Rol created successfully",
      newRol,
    });
  } catch (error) {
    console.error("Error creating Rol:", error);
    res.status(500).json({ message: "Error creating Rol" });
  }
};

/**
 * Delete a specific rol given it id.
 */
export const deleteRol = async (req, res) => {
  const { rol_id } = req.params;

  try {
    if (!uuidValidate(rol_id)) {
      return res.status(400).json({ message: "Invalid rol_id" });
    }

    const rol = await Lista_Roles.findByPk(rol_id);
    if (!rol) {
      return res.status(404).json({ message: "rol not found" });
    }

    await rol.destroy();
    res.status(200).json({ message: "rol deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: "Error deleting rol" });
  }
};
