/**
 * Utility function to validate the format of a given rut and check if it's valid.
 * @param {string} rut_sin_dv - The "rut sin dv" provided by the user.
 * @param {string} digito_verificador - The "Digito verificador" provided by the user.
 * @returns {boolean} - True if the rut is valid, false otherwise.
 */
export const validarRut = (rut_sin_dv, digito_verificador) => {
    try {
        if (rut_sin_dv !== undefined && typeof rut_sin_dv.toString === 'function'
            && digito_verificador !== undefined && typeof digito_verificador.toString === 'function') {


            rut_sin_dv = rut_sin_dv.toString();
            rut_sin_dv = rut_sin_dv.replace(/[.\-\s]/g, ''); // Remove dots, dashes, and spaces
            if (!/^[0-9]+[0-9kK]{1}$/.test(rut_sin_dv)) return false; // Incorrect format


            digito_verificador = digito_verificador.toString();
            if (!/^\d$|^k$/i.test(digito_verificador)) return false;


            // Separate verification digit
            var num = rut_sin_dv;
            var dig = digito_verificador;

            // Calculate verification digit
            var suma = 0;
            var multiplo = 2;

            // For each digit of the number, from right to left
            for (var i = num.length - 1; i >= 0; i--) {
                suma += parseInt(num.charAt(i)) * multiplo;
                if (multiplo === 7) multiplo = 2;
                else multiplo++;
            }
            // Calculate the remainder of the division
            var resto = suma % 11;
            var dv = 11 - resto;
            // Verify the verification digit
            if (dv === 11) dv = 0;
            else if (dv === 10) dv = 'k';
            else dv = dv.toString();

            // Return true if the verification digit and the calculated digit are equal
            return dv == dig.toLowerCase() || dv == dig.toUpperCase();
        } else {
            return false;
        }
    } catch (error) {
        console.error('Error checking if rut is valid', error);

        throw error;
    }
}