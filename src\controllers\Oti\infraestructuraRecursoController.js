import { checkAtributosNecesarios } from "../../utils/checkAtributosNecesarios.js";
import InfraestructuraRecurso from "../../models/Oti/InfraestructuraRecurso.js";
import Usuario from "../../models/Usuario/usuario.js";
import Acceso from "../../models/Usuario/acceso.js";
import { Op } from "sequelize";
import { Sequelize } from "sequelize";
import InmueblePermanente from "../../models/Oti/inmueblePermanente.js";
import InmuebleRestringido from "../../models/Oti/inmuebleRestringido.js";
import Biblioteca from "../../models/Oti/biblioteca.js";
import LibrosBasesDigitales from "../../models/Oti/librosBasesDigitales.js";
import Predio from "../../models/Oti/predio.js";
import PlataformaVirtual from "../../models/Oti/plataformaVirtual.js";
import { checkTokenLocal, getEmailFromToken } from "../../auth/auth.js";
import { sendTestEmail } from "../../utils/emailTest.js";
import { sequelize } from "../../database/database.js";
import { checkPermisosUsuario } from "../../utils/checkPermisosUsuario.js";

const checkDatabase = async () => {
  try {
    console.log("Checking database connection...");
    await sequelize.authenticate();
    console.log("Database is connected successfully.");
  } catch (error) {
    console.error("Database connection error:", error);
    return res.status(400).json({ message: "Database not connected" });
  }
};

/**
 * Handler to fetch an "infraestructuraRecurso" given a year.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const getInfraestructuraRecurso = async (req, res) => {
  const { anio_proceso } = req.params;
  const userToken = req.headers.authorization;

  if (!userToken) {
    return res.status(401).json({
      message: "Authorization header is missing",
    });
  }

  try {
    // Cast anio_proceso to a number and check if it's an integer
    const anioProcesoNumber = Number(anio_proceso);
    if (!Number.isInteger(anioProcesoNumber)) {
      return res
        .status(400)
        .json({ message: "anio_proceso must be an integer" });
    }
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    // Check the database connection
    await checkDatabase();
    // Check if the InfraestructuraRecurso exists
    const infraestructuraRecurso = await InfraestructuraRecurso.findOne({
      where: { anio_proceso: anioProcesoNumber },
      distinct: true,
      include: [
        { model: InmueblePermanente },
        { model: InmuebleRestringido },
        { model: Biblioteca },
        { model: LibrosBasesDigitales },
        { model: Predio },
        { model: PlataformaVirtual },
      ],
      order: [["anio_proceso", "ASC"]],
    });
    if (!infraestructuraRecurso) {
      return res
        .status(404)
        .json({ message: "InfraestructuraRecurso not found" });
    }
    // Return "bea" with filtered values
    res.status(200).send(infraestructuraRecurso);
  } catch (error) {
    console.error("Error fetching InfraestructuraRecurso:", error);
    res.status(500).send("Error fetching InfraestructuraRecurso");
  }
};

/**
 * Handler to fetch all "infraestructuraRecurso" from the database and return them in a sorted order.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const getAllInfraestructuraRecurso = async (req, res) => {
  const userToken = req.headers.authorization;

  if (!userToken) {
    return res.status(401).json({
      message: "Authorization header is missing",
    });
  }

  try {
    // Check the database connection
    await checkDatabase();
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    console.log("Database connection has been established successfully.");
    // Fetch all InfraestructuraRecurso records, sorted by anio_proceso in ascending order
    const infraestructuraRecursos = await InfraestructuraRecurso.findAll({
      distinct: true,
      include: [
        { model: InmueblePermanente },
        { model: InmuebleRestringido },
        { model: Biblioteca },
        { model: LibrosBasesDigitales },
        { model: Predio },
        { model: PlataformaVirtual },
      ],
      order: [["anio_proceso", "ASC"]], // Sorting by "anio_proceso"
    });

    // If no records are found, return a 404 status
    if (infraestructuraRecursos.length === 0) {
      return res
        .status(404)
        .json({ message: "No InfraestructuraRecursos found" });
    }

    // Return the records
    res.status(200).json(infraestructuraRecursos);
  } catch (error) {
    console.error("Error fetching all InfraestructuraRecursos:", error);
    res.status(500).send("Error fetching InfraestructuraRecursos");
  }
};


/**
 * Handler to create an "infraestructuraRecurso" in the database.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const postInfraestructuraRecurso = async (req, res) => {
  const userToken = req.headers.authorization;

  const { anio_proceso } = req.body;

  // Define required attributes
  const requiredAttributes = ["anio_proceso"];

  try {
    // Get the email from the token
    const creador_email = await getEmailFromToken(userToken);
    if (!creador_email) {
      return res.status(401).json({
        message: "Invalid token: Unable to retrieve email",
      });
    }
    // Check the database connection
    await checkDatabase();

    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredAttributes
    );
    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }

    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }

    // Cast anio_proceso to a number and check if it's an integer
    const anioProcesoNumber = Number(anio_proceso);
    if (!Number.isInteger(anioProcesoNumber)) {
      return res
        .status(400)
        .json({ message: "anio_proceso must be an integer" });
    }
    if (!userToken) {
      return res.status(401).json({
        message: "Authorization header is missing",
      });
    }
    // Check if an InfraestructuraRecurso with the same anio_proceso already exists
    const existingInfraestructuraRecurso = await InfraestructuraRecurso.findOne(
      {
        where: { anio_proceso: anioProcesoNumber },
      }
    );
    if (existingInfraestructuraRecurso) {
      return res.status(409).json({
        message: "InfraestructuraRecurso with this anio_proceso already exists",
      });
    }

    // Validate the user's roles
    const validRoles = [
      "Encargado",
      "Usuario General",
      "Administrador Sistema",
    ];

    const hasAccess = await checkPermisosUsuario(
      creador_email,
      validRoles,
      "OTI"
    );
    if (!hasAccess) {
      return res.status(401).json({
        message:
          "Unauthorized access: User does not have the required permissions",
      });
    }

    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: creador_email },
      },
    });
    if (!usuario) {
      return false;
    }

    // Create the InfraestructuraRecurso record (infraestructuraRecurso_id is generated automatically)
    const newInfraestructuraRecurso = await InfraestructuraRecurso.create({
      anio_proceso: anioProcesoNumber,
      creador_email,
      creador_id: usuario.usuario_id,
      is_finalized: false,
      validated_by: null,
    });

    // Return the created record
    res.status(201).json(newInfraestructuraRecurso);
  } catch (error) {
    console.error("Error creating InfraestructuraRecurso:", error);
    res.status(500).json({ message: "Error creating InfraestructuraRecurso" });
  }
};

/**
 * Handler to create an "infraestructuraRecurso" in the database.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const confirmInfraestructuraRecurso = async (req, res) => {
  const userToken = req.headers.authorization;
  const { anio_proceso, is_finalized, validated_by } = req.body;

  // Define required attributes
  const requiredAttributes = ["anio_proceso", "is_finalized", "validated_by"];

  try {
    // Check the database connection
    await checkDatabase();
    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredAttributes
    );
    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    // Cast anio_proceso to a number and check if it's an integer
    const anioProcesoNumber = Number(anio_proceso);
    if (!Number.isInteger(anioProcesoNumber)) {
      return res
        .status(400)
        .json({ message: "anio_proceso must be an integer" });
    }
    // Check if an InfraestructuraRecurso with the same anio_proceso already exists
    const existingInfraestructuraRecurso = await InfraestructuraRecurso.findOne(
      {
        where: { anio_proceso: anioProcesoNumber },
      }
    );
    if (!existingInfraestructuraRecurso) {
      return res.status(404).json({
        message: "InfraestructuraRecurso with this anio_proceso doesnt exists",
      });
    }

    // Validate that validated_by exists in the Usuario model (case-insensitive)
    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: validated_by },
      },
    });
    if (!usuario) {
      return res.status(404).json({
        message:
          "User not found or does not have access to the specified resource",
      });
    }

    // Validate the user's roles
    const validRoles = ["Encargado", "Administrador Sistema"];

    const hasAccess = await checkPermisosUsuario(
      validated_by,
      validRoles,
      "OTI"
    );
    if (!hasAccess) {
      return res.status(401).json({
        message:
          "Unauthorized access: User does not have the required permissions",
      });
    }

    // Update the InfraestructuraRecurso record
    const [updated] = await InfraestructuraRecurso.update(
      {
        is_finalized,
        validated_by,
      },
      { where: { anio_proceso: anioProcesoNumber } }
    );

    if (updated) {
      // Send email if update is successful
      try {
        // Send email if update is successful
        await sendTestEmail(
          "<EMAIL>",
          "Validación Infraestructura y Recursos proceso " + anioProcesoNumber,
          "Se realizo la validacion de la Infraestructura y Recursos del proceso " +
            anioProcesoNumber +
            " por el usuario " +
            validated_by
        );
      } catch (emailError) {
        console.error("Error sending email:", emailError);
        return res.status(500).json({
          message: "Update successful, but failed to send email",
          error: emailError.message,
        });
      }

      // Return the updated record
      const updatedInfraestructuraRecurso =
        await InfraestructuraRecurso.findOne({
          where: { anio_proceso: anioProcesoNumber },
        });
      return res.status(201).json(updatedInfraestructuraRecurso);
    } else {
      return res
        .status(404)
        .json({ message: "InfraestructuraRecurso record not found" });
    }
  } catch (error) {
    console.error("Error creating InfraestructuraRecurso:", error);
    res.status(500).json({ message: "Error creating InfraestructuraRecurso" });
  }
};

/**
 * Handler to delete an "infraestructuraRecurso" from the database.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const deleteInfraestructuraRecurso = async (req, res) => {
  const { anio_proceso } = req.params;
  const userToken = req.headers.authorization;
  // Cast anio_proceso to a number and check if it's an integer
  const anioProcesoNumber = Number(anio_proceso);
  if (!Number.isInteger(anioProcesoNumber)) {
    return res.status(400).json({ message: "anio_proceso must be an integer" });
  }

  try {
    // Check the database connection
    await checkDatabase();
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }

    // Get the email from the token
    const userEmail = await getEmailFromToken(userToken);
    if (!userEmail) {
      return res.status(401).json({
        message: "Invalid token: Unable to retrieve email",
      });
    }

    // Validate that creador_email exists in the Usuario model (case-insensitive)
    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: userEmail },
      },
    });
    if (!usuario) {
      return res.status(404).json({
        message:
          "User not found or does not have access to the specified resource",
      });
    }

    // Validate the user's roles
    const validRoles = [
      "Encargado",
      "Administrador Sistema",
    ];

    const hasAccess = await checkPermisosUsuario(
      userEmail,
      validRoles,
      "OTI"
    );
    if (!hasAccess) {
      return res.status(401).json({
        message:
          "Unauthorized access: User does not have the required permissions",
      });
    }
    // Find the InfraestructuraRecurso record by anio_proceso
    const infraestructuraRecurso = await InfraestructuraRecurso.findOne({
      where: { anio_proceso: anioProcesoNumber },
    });

    // Check if the record exists
    if (!infraestructuraRecurso) {
      return res.status(404).json({
        message: "InfraestructuraRecurso not found",
      });
    }

    // If the infarestructuraRecurso is already finalized, then the biblioteca can't be deleted
    if (infraestructuraRecurso.is_finalized == true) {
      return res.status(403).json({
        message: "Can't delete of an already validated Infraestructura",
      });
    }

    // Attempt to delete the record
    await infraestructuraRecurso.destroy({
      where: { anio_proceso: anioProcesoNumber },
    });

    // Return a success message
    res.status(200).json({
      message: "InfraestructuraRecurso successfully deleted",
    });
  } catch (error) {
    console.error("Error deleting InfraestructuraRecurso:", error);

    // Check if the error is a foreign key constraint error (ON DELETE RESTRICT)
    if (error instanceof Sequelize.ForeignKeyConstraintError) {
      return res.status(409).json({
        message:
          "Cannot delete InfraestructuraRecurso: It is referenced by other records.",
      });
    }
    // For other types of errors
    res.status(500).json({ message: "Error deleting InfraestructuraRecurso" });
  }
};
