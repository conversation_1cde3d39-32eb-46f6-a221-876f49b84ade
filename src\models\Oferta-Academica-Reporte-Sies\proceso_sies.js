import { Model, DataTypes } from "sequelize";
import sequelize from "../../database/database.js";

class ProcesoSies extends Model {}

ProcesoSies.init({
    proceso_sies_id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
        allowNull: false
    },
    nombre: {
        type: DataTypes.STRING,
        allowNull: false
    },
    codigo: {
        type: DataTypes.STRING,
        allowNull: false
    },
    etapa: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 1
    },
    anio_proceso: {
        type: DataTypes.INTEGER,
        allowNull: true
    },
    fecha_inicio: {
        type: DataTypes.DATE,
        allowNull: true
    },
    fecha_termino: {
        type: DataTypes.DATE,
        allowNull: true
    },
    version_lock: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 1
    }
}, {
    sequelize,
    modelName: 'ProcesoSies',
    tableName: 'proceso_sies',
    freezeTableName:true
});



export default ProcesoSies;