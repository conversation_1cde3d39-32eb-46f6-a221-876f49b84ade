import { Model, DataTypes } from "sequelize";
import sequelize from "../../../database/database.js";

class MU_pregrado_estudiante extends Model {}

MU_pregrado_estudiante.init(
  {
    mu_pregrado_estudiante_id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    tipo_doc: {
      type: DataTypes.STRING,
      allowNull: false,
      comment:
        "Tipo de documento de identificacion del matriculado. - P: Pasaporte. R: RUT. - Usar letras mayusculas.",
    },
    n_doc: {
      type: DataTypes.STRING,
      allowNull: false,
      comment:
        "Numero de documento de identificacion del matriculado. - No aplica. - Usar letras mayusculas y numeros.",
    },
    dv: {
      type: DataTypes.STRING,
      allowNull: true,
      comment:
        "Digito verificador del RUT. - No aplica. - Usar numeros del 0 al 9 y letra K mayuscula.",
    },
    primer_apellido: {
      type: DataTypes.STRING,
      allowNull: false,
      comment:
        "Primer Apellido. - No aplica. - Usar letras mayusculas de la A a la Z, sin acentos, tambien se permiten dieresis y guion.",
    },
    segundo_apellido: {
      type: DataTypes.STRING,
      allowNull: true,
      comment:
        "Segundo Apellido. - No aplica. - Usar letras mayusculas de la A a la Z, sin acentos, tambien se permiten dieresis y guion.",
    },
    nombre: {
      type: DataTypes.STRING,
      allowNull: false,
      comment:
        "Nombres. - No aplica. - Usar letras mayusculas de la A a la Z, sin acentos, tambien se permiten dieresis y guion.",
    },
    sexo: {
      type: DataTypes.STRING,
      allowNull: false,
      comment:
        "Corresponde a la condicion de mujer, hombre o no binario de la persona reportada. - H: Hombre. M: Mujer. NB: No Binario. - Usar letras mayusculas.",
    },
    fech_nac: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: "Fecha de nacimiento. - No aplica. - Usar formato dd/mm/aaaa.",
    },
    nac: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: "Pais de nacionalidad. - Del 1 al 197. - Usar solo numeros.",
    },
    pais_est_sec: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment:
        "Pais donde completo sus estudios secundarios. - Del 1 al 197. - Usar solo numeros.",
    },
    cod_sed: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment:
        "Codigo de sede de la IES en la que se encuentra matriculado. - Codigos correspondientes a la institucion. - Usar solo numeros.",
    },
    cod_car: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment:
        "Codigo de carrera de la IES en la que se encuentra matriculado. - Codigos correspondientes a la institucion. - Usar solo numeros.",
    },
    modalidad: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment:
        "Modalidad de la carrera o programa. - Codigos correspondientes a la institucion. - Usar solo numeros.",
    },
    jor: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment:
        "Jornada de la carrera o programa. - Codigos correspondientes a la institucion. - Usar solo numeros.",
    },
    version: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment:
        "Version de la carrera o programa. - Codigos correspondientes a la institucion. - Usar solo numeros.",
    },
    for_ing_act: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment:
        "Via de admision de la carrera o programa. - 1: Ingreso Directo (Regular). 2: Continuadad de Plan Comun o Bachillerato. 3: Cambio Interno. 4: Cambio Externo. 5: Ingreso por Reconocimiento de Aprendizajes Previos. 6: Ingreso especial para estudiantes extranjeros. 7: Ingreso a traves del Programa PACE. 8: Ingreso a traves de Programas de Inclusion. 9: Acceso por caracteristicas especiales (Deportista, Artista, Hijo de profesional de la IES, Pueblos Originarios o Diplomaticos). 10: Otras formas de ingreso. 11: Articulacion de TNS a carrera profesional. - Usar solo numeros.",
    },
    anio_ing_act: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment:
        "Año de ingreso a la carrera actual. - de 1990 a 2024. - Usar solo numeros.",
    },
    sem_ing_act: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment:
        "Semestre de ingreso a la carrera actual. - de 1 a 2. - Usar solo numeros.",
    },
    anio_ing_ori: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment:
        "Año de ingreso a la carrera de origen. - de 1980 a 2024 y 1900. - Usar solo numeros.",
    },
    sem_ing_ori: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment:
        "Semestre de ingreso a la carrera de origen. - de 1 a 2 y 0. - Usar solo numeros.",
    },
    asi_ins_ant: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment:
        "Asignaturas inscritas en el ultimo periodo(año). Considera asignaturas aprobadas y reprobadas del periodo y no considera asignaturas convalidadas u homologadas del peridodo. - de 0 a 99. - Usar solo numeros.",
    },
    asi_apr_ant: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment:
        "Asignaturas aprobadas en el ultimo periodo (año). Considera asignaturas aprobadas y no considera asignaturas convalidadas u homologadas del periodo. - de 0 a 99. - Usar solo numeros.",
    },
    prom_pri_sem: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment:
        "Promedio de notas de asignaturas cursadas en el primer semestre del año anterior. Informacion utilizada por Junaeb. - de 100 a 700 y 0. - Usar solo numeros.",
    },
    prom_seg_sem: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment:
        "Promedio de notas de asignaturas cursadas en el segundo semestre del año anterior. Informacion utilizada por Junaeb. - de 100 a 700 y 0. - Usar solo numeros.",
    },
    asi_ins_his: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment:
        "Asignaturas inscritas desde el inicio de la carrera (tanto asignaturas aprobadas como reprobadas). Las Asignaturas reprobadas deben contarse todas las veces como fueron reprobadas. Se deben incorporar las asignaturas convalidadas u homologadas de la carrera actual. - de 0 a 200. - Usar solo numeros.",
    },
    asi_apr_his: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment:
        "Asignaturas aprobadas desde el inicio de la carrera. Se deben incorporar las asignaturas convalidadas u homologadas de la carrera actual. - de 0 a 200. - Usar solo numeros.",
    },
    niv_aca: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment:
        "Semestre curricular que el matriculado se encuentra cursando. Corresponde a la ubicación en la malla curricular de la mayoria de las asignaturas que el matriculado se encuentra cursando. - de 1 a duracion carrera. - Usar solo numeros.",
    },
    sit_fon_sol: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment:
        "Indica si el estudiante mantiene o no la situacion socioeconomica con la que obtuvo FSCU. - 0: No cumple / No aplica. 1: Si cumple. 2: No presenta documentacion. - Usar solo numeros.",
    },
    sus_pre: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment:
        "Numero de semestres de suspensiones que tiene el matriculado previo al año actual. - de 0 a 99. - Usar solo numeros.",
    },
    fecha_matricula: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment:
        "Fecha en que el o la estudiante ha sido inscrito en la matricula de la institucion. - No aplica. En caso de no contar con el dato: 01/01/1900. - Usar formato dd/mm/aaaa.",
    },
    reincorporacion: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment:
        "Indica si el estudiante se reincorpora a su carrera en segundo semestre luego de suspender el primer semestre. Solo puede ser informado con valor 1 durante la ultima carga de matricula del periodo. - 0: No se reincorpora / No aplica. 1:Si se reincorpora. - Usar solo numeros.",
    },
    vig: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment:
        "Estado de la vigencia de la matricual informada, de acuerdo con la reglamentacion interna de cada institucion. - 0: Estudiante sin matricula. 1: Estudiante con matricula vigente. 2: Estudiante egresado con matricula vigente. - Usar solo numeros.",
    },
    creador_email: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isEmail: true, // Ensures the email format is valid
      },
      comment:
        "Email del usuario que crea el registro. - No aplica. - Usar formato de correo electronico.",
    },
    etapa_proceso: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: "Etapa en la que es cargado el estudiante.",
    }
  },
  {
    sequelize,
    modelName: "MU_pregrado_estudiante",
    freezeTableName: true,
  }
);

export default MU_pregrado_estudiante;
