/**
 * Check if all required attributes are present in the request body.
 * @param {Object} body - The request body.
 * @param {Array<string>} requiredAttributes - An array of required attribute names.
 * @returns {Array<string>} - Returns an array of missing attribute names. If no attributes are missing, returns an empty array.
 */
export const checkAtributosNecesarios = (body, requiredAttributes) => {
    const missingAttributes = requiredAttributes.filter(attr => !body.hasOwnProperty(attr) || body[attr] === undefined || body[attr] === '');
    return missingAttributes;
};