// src/models/Mantenedores/lista_acciones.js
import { Model, DataTypes } from "sequelize";
import sequelize from "../../database/database.js";

class Lista_Acciones extends Model {}

Lista_Acciones.init(
  {
    accion_id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    nombre_accion: {
      type: DataTypes.STRING,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "Lista_Acciones",
    freezeTableName: true,
  }
);

export default Lista_Acciones;
