import { Model, DataTypes } from "sequelize";
import sequelize from "../../database/database.js";

class InmueblePermanente extends Model {}

InmueblePermanente.init(
  {
    inmueblePermanente_id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    tipo_infraestructura: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    nombre_identificacion: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    comuna: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    direccion_inmueble: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    situacion_tenencia: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    anio_inicio_uso_inmueble: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    uso_exclusivo: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    porcentaje_uso: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    nombre_institucion_comparte: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    fecha_inicio_tenencia: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    fecha_termino: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    descripcion_otra_tenencia: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    funcion_docencia: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    funcion_investigacion: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    funcion_extension: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    funcion_adm_oficinas: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    funcion_otras: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    desc_otras_funciones: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    total_m2_terreno: {
      type: DataTypes.DOUBLE,
      allowNull: false,
    },
    total_m2_edificados: {
      type: DataTypes.DOUBLE,
      allowNull: false,
    },
    total_salas_clases: {
      type: DataTypes.DOUBLE,
      allowNull: false,
    },
    capacidad_salas_clases: {
      type: DataTypes.DOUBLE,
      allowNull: false,
    },
    total_m2_salas_clases: {
      type: DataTypes.DOUBLE,
      allowNull: false,
    },
    total_auditorios: {
      type: DataTypes.DOUBLE,
      allowNull: false,
    },
    capacidad_auditorios: {
      type: DataTypes.DOUBLE,
      allowNull: false,
    },
    total_m2_auditorios: {
      type: DataTypes.DOUBLE,
      allowNull: false,
    },
    total_laboratorios: {
      type: DataTypes.DOUBLE,
      allowNull: false,
    },
    total_m2_laboratorios: {
      type: DataTypes.DOUBLE,
      allowNull: false,
    },
    total_talleres: {
      type: DataTypes.DOUBLE,
      allowNull: false,
    },
    total_m2_talleres: {
      type: DataTypes.DOUBLE,
      allowNull: false,
    },
    total_pc_nb_disponible: {
      type: DataTypes.DOUBLE,
      allowNull: false,
    },
    total_m2_casinos_cafeterias: {
      type: DataTypes.DOUBLE,
      allowNull: false,
    },
    total_m2_areas_verdes: {
      type: DataTypes.DOUBLE,
      allowNull: false,
    },
    vigencia: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    // Sequelize optimistic lock to prevent data override
    version_lock: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
    },
    anio_proceso: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    creador_email: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    creador_id: {
      type: DataTypes.UUID,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "InmueblePermanente",
    freezeTableName: true,
  }
);

export default InmueblePermanente;
