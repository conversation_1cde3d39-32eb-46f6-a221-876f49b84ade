import { Model, DataTypes } from "sequelize";
import sequelize from "../../../database/database.js";
import MU_pregrado_estudiante from "./mu_pregrado_estudiante.js";
import MU_pregrado_fecha from "./mu_pregrado_fecha.js";

class MU_pregrado_anio_proceso extends Model {}

MU_pregrado_anio_proceso.init(
  {
    mu_pregrado_anio_proceso_id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    anio_proceso: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: true,
    },
    is_finalized: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    upploaded_by: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    // Sequelize optimistic lock to prevent data override
    version_lock: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
    },
    creador_email: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isEmail: true, // Ensures the email format is valid
      },
    },
    etapa_actual: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
    },
    creador_id: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    fecha_validacion_1: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: "Fecha en que la etapa 1 actual fue validada.",
    },
    fecha_validacion_2: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: "Fecha en que la etapa 2 actual fue validada.",
    },
    fecha_validacion_3: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: "Fecha en que la etapa 3 actual fue validada.",
    },
    fecha_validacion_4: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: "Fecha en que la etapa 4 actual fue validada.",
    },
    fecha_validacion_5: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: "Fecha en que la etapa 5 actual fue validada.",
    },
    fecha_carga_1: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: "Fecha de creación del registro en la etapa 1.",
    },
    fecha_carga_2: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: "Fecha de creación del registro en la etapa 2.",
    },
    fecha_carga_3: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: "Fecha de creación del registro en la etapa 3.",
    },
    fecha_carga_4: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: "Fecha de creación del registro en la etapa 4.",
    },
    fecha_carga_5: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: "Fecha de creación del registro en la etapa 5.",
    },
  },
  {
    sequelize,
    modelName: "MU_pregrado_anio_proceso",
    freezeTableName: true,
  }
);

// One-to-Many relationship with "MU_postgrado_postitulo_estudiante"
MU_pregrado_anio_proceso.hasMany(MU_pregrado_estudiante, {
  foreignKey: "mu_pregrado_anio_proceso_id",
  onDelete: "RESTRICT",
  onUpdate: "CASCADE",
});
MU_pregrado_estudiante.belongsTo(MU_pregrado_anio_proceso, {
  foreignKey: "mu_pregrado_anio_proceso_id",
  onDelete: "RESTRICT",
  onUpdate: "CASCADE",
});
MU_pregrado_fecha.belongsTo(MU_pregrado_anio_proceso, {
  foreignKey: "mu_pregrado_anio_proceso_id",
  onDelete: "RESTRICT",
  onUpdate: "CASCADE",
});
MU_pregrado_anio_proceso.hasMany(MU_pregrado_fecha, {
  foreignKey: "mu_pregrado_anio_proceso_id",
  onDelete: "RESTRICT",
  onUpdate: "CASCADE",
});

export default MU_pregrado_anio_proceso;
