// src/models/Usuario/acceso.js
import { Model, DataTypes } from "sequelize";
import sequelize from "../../database/database.js";
import ProcesoSies from "./proceso_sies.js";

class OA_SIES extends Model {}

OA_SIES.init(
  {
    oa_sies_id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    etapa_actual: {
      type: DataTypes.CHAR(1),
      allowNull: true,
    },
    anio_a_reportar: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment:
        "Equivale a la fecha de la oferta academica del proximo anio a reportar",
    },
    autor: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    cantidad_matricula_dfe: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    cantidad_beneficiado_dfe: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    version_lock: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
    },
    codigo_unico: {
      type: DataTypes.STRING,
      allowNull: true,
      comment:
        "Codigo unico utilizado de forma interna por la universidad para identificar el registro",
    },
    cod_sede: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    nombre_sede: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    cod_carrera: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    nombre_carrera: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    modalidad: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    cod_jornada: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    version: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    cod_tipo_plan_carrera: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    caracteristicas_tipo_plan: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    duracion_estudios: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    duracion_titulacion: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    duracion_total: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    regimen: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    duracion_regimen: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    nombre_titulo: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    nombre_grado: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    cod_nivel_global: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    cod_nivel_carrera: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    cod_demre: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    anio_inicio: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    acreditacion: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    elegible_beca_pedagogia: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    ped_med_odont_otro: {
      type: DataTypes.CHAR(1),
      allowNull: true,
    },
    requisito_ingreso: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    semestres_reconocidos: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    area_actual: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    area_destino_agricultura: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    area_destino_ciencias: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    area_destino_cs_sociales: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    area_destino_educacion: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    area_destino_humanidades: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    area_destino_ingenieria: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    area_destino_salud: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    area_destino_servicios: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    ponderacion_nem: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    ponderacion_ranking: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    ponderacion_c_lectora: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    ponderacion_matematica_1: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    ponderacion_matematica_2: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    ponderacion_historia: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    ponderacion_ciencias: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    ponderacion_otros: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    vacantes_primer_semestre: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    vacantes_segundo_semestre: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    vacantes_pace: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    malla_curricular: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    perfil_egreso: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    texto_requerido_ingreso: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    otros_requisitos: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    mail_difusion_carrera: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    formato_valor: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    valor_matricula_anual: {
      type: DataTypes.DOUBLE,
      allowNull: true,
    },
    costo_titulacion: {
      type: DataTypes.DOUBLE,
      allowNull: true,
    },
    valor_certificado_diploma: {
      type: DataTypes.DOUBLE,
      allowNull: true,
    },
    arancel_anual: {
      type: DataTypes.DOUBLE,
      allowNull: true,
    },
    vigencia_carrera: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    proceso_sies_id: {
      type: DataTypes.UUID,
      allowNull: false,
  },
  },
  {
    sequelize,
    modelName: "OA_SIES",
    freezeTableName: true,
  }
);
ProcesoSies.hasMany(OA_SIES, { foreignKey: 'proceso_sies_id', onDelete: 'RESTRICT' });

OA_SIES.belongsTo(ProcesoSies, { foreignKey: 'proceso_sies_id', onDelete: 'RESTRICT' });


export default OA_SIES;
