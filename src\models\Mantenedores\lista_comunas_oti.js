// src/models/Mantenedores/lista_comunas.js
import { Model, DataTypes } from "sequelize";
import sequelize from "../../database/database.js";

class Lista_Comunas_OTI extends Model {}

Lista_Comunas_OTI.init(
  {
    comuna_id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    nombre_comuna: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    creador_email: {
      type: DataTypes.STRING,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "Lista_Comunas_OTI",
    freezeTableName: true,
  }
);

export default Lista_Comunas_OTI;
