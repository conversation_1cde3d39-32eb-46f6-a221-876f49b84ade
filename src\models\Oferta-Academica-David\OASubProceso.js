import { Model, DataTypes } from "sequelize";
import sequelize from "../../database/database.js";
import OAProceso from "./OAProceso.js";


class OASubProceso extends Model {}

OASubProceso.init({
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
    },
    proceso_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: OAProceso,
            key: 'id' // Key in the referenced table
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
    },
    tipo: {
        type: DataTypes.INTEGER,
        allowNull: false
    },
    fecha_inicial: {
        type: DataTypes.DATE,
        allowNull: true
    },
    fecha_final: {
        type: DataTypes.DATE,
        allowNull: true
    },
    validado: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false
    },
    etapa_actual: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    // subido_por: {
    //     type: DataTypes.STRING,
    //     allowNull: true,
    //     validate: { 
    //       isEmail: true, // Ensures the email format is valid
    //     },
    //   },
        creado_por: {
        type: DataTypes.STRING,
        allowNull: true,
        validate: { 
          isEmail: true, // Ensures the email format is valid
        },
      },
    version_lock: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 1
    }
}, {
    sequelize,
    modelName: 'OASubProceso',
    tableName: 'oa_sub_procesos',
    schema: 'oferta_academica',

    freezeTableName: true
});
OAProceso.hasMany(OASubProceso, { foreignKey: 'proceso_id', onDelete: 'CASCADE' });
OASubProceso.belongsTo(OAProceso, { foreignKey: 'proceso_id' });

export default OASubProceso;