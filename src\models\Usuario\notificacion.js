// models/notificacion.js
import { Model, DataTypes } from "sequelize";
import sequelize from "../../database/database.js";
import Usuario from "../Usuario/usuario.js";

class Notificacion extends Model { }

Notificacion.init({
    notificacion_id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    titulo: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    mensaje: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    tipo: {
      type: DataTypes.STRING,
      allowNull: false,
      // Example types: 'info', 'warning', 'success', etc.
    },
    estado: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    ocultar: {
      type: DataTypes.BOOLEAN,
      defaultValue: true, // False for hidden, true for non-hidden
    },
    eliminar: {
      type: DataTypes.BOOLEAN,
      defaultValue: false, // False for non-deleted, true for deleted (for the user, it will not be deleted)
    },
    creador_email: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    dest_email: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    destinatario: {
      type: DataTypes.UUID,
      defaultValue: null,
    },
  },
  {
    sequelize,
    modelName: "Notificacion",
    freezeTableName: true
}
);

// Relations (Notificacion belongs to Usuario)
Notificacion.belongsTo(Usuario, { foreignKey: "usuario_id" });
Usuario.hasMany(Notificacion, { foreignKey: "usuario_id" });

export default Notificacion;
