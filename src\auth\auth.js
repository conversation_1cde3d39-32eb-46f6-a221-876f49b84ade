// src/auth/auth.js
import jwksClient from "jwks-rsa";
import jwt from "jsonwebtoken";
import { config } from "../config.js";
import Usuario from "../models/Usuario/usuario.js";
import Acceso from "../models/Usuario/acceso.js";
import { Model } from "sequelize";
import { Op } from "sequelize";
import { sequelize } from "../database/database.js";

const client = jwksClient({
  jwksUri: `https://login.microsoftonline.com/${config.tenantId}/discovery/keys`,
});

function getKey(header) {
  return new Promise((resolve, reject) => {
    client.getSigningKey(header.kid, (err, key) => {
      if (err) {
        console.error("Error getting signing key:", err);
        return reject(err);
      }
      if (!key) {
        console.error("No key found for kid:", header.kid);
        return reject(new Error("No key found"));
      }
      const signingKey = key.getPublicKey
        ? key.getPublicKey()
        : key.rsaPublicKey;
      resolve(signingKey);
    });
  });
}

export const checkToken = async (req, res, next) => {
  const token = req.headers["authorization"];
  console.log(token);
  if (!token) {
    return res.status(403).send("A token is required for authentication");
  }
  const tokenParts = token.split(" ");
  if (tokenParts.length !== 2 || tokenParts[0] !== "Bearer") {
    return res.status(401).send("Invalid token format");
  }
  const accessToken = tokenParts[1];
  try {
    console.log("VALIDATION OF THE DATABASE CONNECTION IN 'CHECK'");
    await sequelize.authenticate();
    const decodedHeader = jwt.decode(accessToken, { complete: true }).header;
    const key = await getKey(decodedHeader);
    jwt.verify(
      accessToken,
      key,
      {
        audience: config.clientId,
        issuer: `https://login.microsoftonline.com/${config.tenantId}/v2.0`,
        algorithms: ["RS256"],
      },
      async (err, decoded) => {
        if (err) {
          console.log("Token verification error:", err.message);
          return res.status(401).send("Invalid token");
        }
        const currentTime = new Date();
        const decodedHeader2 = jwt.decode(accessToken, { complete: true });

        // Get the expiration time from the decoded token (exp is in seconds, so multiply by 1000 to convert to ms)
        const expirationTime = decodedHeader2.payload.exp * 1000;

        // Calculate the difference in milliseconds
        const remainingTime = expirationTime - currentTime.getTime();

        // Convert remaining time into seconds, minutes, and hours
        const remainingSeconds = Math.floor(remainingTime / 1000);
        const remainingMinutes = Math.floor(remainingSeconds / 60);
        const remainingHours = Math.floor(remainingMinutes / 60);
        const remainingDays = Math.floor(remainingHours / 24);

        // Log the remaining time in a human-readable format
        console.log(
          `Remaining time: ${remainingDays} days, ${
            remainingHours % 24
          } hours, ${remainingMinutes % 60} minutes, ${
            remainingSeconds % 60
          } seconds.`
        );

        // Retrieve Usuario and acceso from the database
        const user = await Usuario.findOne({
          where: {
            email: { [Op.iLike]: decoded.preferred_username }, // Case-insensitive exact match
          },
          include: [{ model: Acceso, as: "accesos" }],
        });
        if (!user) {
          return res.status(401).send("Usuario not found");
        }

        // Attach user information to req object
        req.user = {
          nombre: user.nombre,
          email: user.email,
          accesos: user.accesos.map((acceso) => ({
            rol: acceso.rol,
            recurso: acceso.recurso,
            accion: acceso.accion,
          })),
        };

        // Set a flag to indicate token is valid
        req.tokenValid = true;

        // Pass control to the next middleware or route handler
        next();
      }
    );
  } catch (error) {
    if (error.name.toLowerCase().includes("sequelize")) {
      console.error("Database connection error");
      return res.status(400).json({ message: "Database not connected" });
    }
    console.error("Error in token verification:", error.message);
    return res.status(401).send("Invalid token");
  }
};

export const checkTokenLocal = async (token) => {
  try {
    if (!token) {
      console.error("Token is missing");
      return false;
    }
    console.log(token);
    const tokenParts = token.split(" ");
    if (tokenParts.length !== 2 || tokenParts[0] !== "Bearer") {
      console.error("Invalid token format");
      return false;
    }

    const accessToken = tokenParts[1];
    const decodedHeader = jwt.decode(accessToken, { complete: true });
    if (!decodedHeader) {
      console.error("Unable to decode token header");
      return false;
    }

    const key = await getKey(decodedHeader.header); // Assume getKey is implemented to fetch signing key
    const decodedToken = jwt.verify(accessToken, key, {
      audience: config.clientId,
      issuer: `https://login.microsoftonline.com/${config.tenantId}/v2.0`,
      algorithms: ["RS256"],
    });

    // Check token expiration
    const currentTime = Date.now();
    const expirationTime = decodedToken.exp * 1000; // Convert `exp` to milliseconds
    if (currentTime >= expirationTime) {
      console.error("Token has expired");
      return false;
    }

    // Retrieve user from database
    const user = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: decodedToken.preferred_username },
      },
      include: [{ model: Acceso, as: "accesos" }],
    });

    if (!user) {
      console.error("User not found");
      return false;
    }

    console.log("Token is valid and user exists:");

    return true; // Token is valid and not expired
  } catch (err) {
    console.error("Error in token validation:", err.message);
    return false;
  }
};
export const getEmailFromToken = async (token) => {
  try {
    if (!token) {
      throw new Error("Token is missing");
    }

    const tokenParts = token.split(" ");
    if (tokenParts.length !== 2 || tokenParts[0] !== "Bearer") {
      throw new Error("Invalid token format");
    }

    const accessToken = tokenParts[1];
    const decodedHeader = jwt.decode(accessToken, { complete: true });
    if (!decodedHeader) {
      throw new Error("Unable to decode token header");
    }

    const key = await getKey(decodedHeader.header); // Retrieve the signing key
    const decodedToken = jwt.verify(accessToken, key, {
      audience: config.clientId,
      issuer: `https://login.microsoftonline.com/${config.tenantId}/v2.0`,
      algorithms: ["RS256"],
    });

    // Extract the preferred_username (email) from the decoded token
    const email = decodedToken.preferred_username;
    if (!email) {
      throw new Error("Preferred username not found in token");
    }

    return email;
  } catch (error) {
    console.error("Error in extracting email from token:", error.message);
    throw error;
  }
};
