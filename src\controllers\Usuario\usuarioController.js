import Acceso from "../../models/Usuario/acceso.js";
import Usuario from "../../models/Usuario/usuario.js";
import { checkAtributosNecesarios } from "../../utils/checkAtributosNecesarios.js";
import { Op } from "sequelize";
import { checkTokenLocal } from "../../auth/auth.js";

/**
 * <PERSON><PERSON> to fetch all "Notificacion" from the database and return them in a sorted order.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const getAllUsuarios = async (req, res) => {
  try {
    const usuarios = await Usuario.findAll({
      attributes: ["nombre", "email"], // Select specific fields
      order: ["createdAt"],
    });

    // Return "bea" with filtered values
    res.status(200).send(usuarios);
  } catch (error) {
    console.error("Error fetching Usuarios:", error);
    res.status(500).send("Error fetching Usuarios");
  }
};

/**
 * <PERSON><PERSON> to fetch all user 'that work underhim' .
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const getUsuariosAreaEncargado = async (req, res) => {
  const { email } = req.params;
  // Define required attributes
  const requiredAttributes = ["email"];

  try {
    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.params,
      requiredAttributes
    );
    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }
    // Validate that "email" exists in the Usuario model (case-insensitive)
    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: email },
      },
    });
    if (!usuario) {
      return res.status(404).json({
        message: "usuario not found",
      });
    }
    const acceso = await Acceso.findAll({
      where: {
        usuario_id: usuario.usuario_id,
      },
    });
    if (!acceso) {
      return res.status(404).json({
        message: "The user dont have access to any module",
      });
    }
    const groupedRoles = new Map();

    for (const item of acceso) {
      if (!groupedRoles.has(item.recurso)) {
        groupedRoles.set(item.recurso, new Set());
      }
      groupedRoles.get(item.recurso).add(item.rol);
    }

    // Convert the `Set` values to arrays
    const result = Array.from(groupedRoles.entries()).map(
      ([recursos, roles]) => ({
        recursos,
        roles: Array.from(roles),
      })
    );
    console.log(result);
    const hasAdminSystem = result.some((entry) =>
      entry.roles.includes("Administrador Sistema")
    );
    console.log(hasAdminSystem);
    if (hasAdminSystem) {
      console.log("user is admin");
      const accesosUsuario = await Usuario.findAll({
        where: {
          usuario_id: {
            [Op.not]: usuario.usuario_id,
          },
        },
        include: [
          {
            model: Acceso,
            as: "accesos", // Specify the Usuario model
          },
        ],
      });
      res.status(200).send(accesosUsuario);
    } else {
      // Extract resources where the role of the user is "Encargado"
      const encargadoResources = result
        .filter((entry) => entry.roles.includes("Encargado"))
        .map((entry) => entry.recursos);
      if (encargadoResources.length > 0) {
        const accesosUsuario = await Usuario.findAll({
          where: {
            usuario_id: {
              [Op.not]: usuario.usuario_id, // Exclude the user with the same "usuario_id"
            },
          },
          include: [
            {
              model: Acceso,
              as: "accesos",
              where: {
                recurso: {
                  [Op.in]: encargadoResources, // Filter resources matching the "encargadoResources" array
                },
              },
            },
          ],
          order: [["nombre", "ASC"]],
        });
        // You can now use `encargadoResources` as needed in your response
        res.status(200).send(accesosUsuario);
      } else {
        console.log("user is not 'Encargado' of any area");
        res.status(404).send("user is not 'Encargado' of any area");
      }
    }
  } catch (error) {
    console.error("Error fetching Usuarios:", error);
    res.status(500).send("Error fetching Usuarios");
  }
};

/**
 * Handler to create an "infraestructuraRecurso" in the database.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const postUsuario = async (req, res) => {
  const { nombre, email, rol, recurso, accion, creador_email, userToken } =
    req.body;

  // Define required attributes
  const requiredAttributes = [
    "nombre",
    "email",
    "rol",
    "recurso",
    "accion",
    "creador_email",
    "userToken",
  ];

  // Check for missing required attributes
  const missingAttributes = checkAtributosNecesarios(
    req.body,
    requiredAttributes
  );
  if (missingAttributes.length > 0) {
    return res.status(400).json({
      message: `Missing required attributes: ${missingAttributes.join(", ")}`,
    });
  }

  try {
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    // Get the data of the current user
    const currentUser = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: creador_email },
      },
      include: {
        model: Acceso,
        as: "accesos",
      },
    });
    if (!currentUser) {
      //user not found
      return res.status(404).json({
        message: "Current user not found",
      });
    }
    // Check if the current user has admin access
    const isAdmin = currentUser?.accesos.some(
      (acceso) => acceso.rol === "Administrador Sistema"
    );

    if (isAdmin) {
    } else {
      // Check if the current user has access to the resource
      const hasAccess = currentUser?.accesos.some(
        (acceso) => acceso.rol === "Encargado" && acceso.recurso === recurso
      );
      if (hasAccess) {
      } else {
        return res
          .status(401)
          .json({ message: "You dont have permission to create a new user" });
      }
    }

    // Validate that "email" dont exists in the Usuario model (case-insensitive)
    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: email },
      },
    });
    if (usuario) {
      return res.status(409).json({
        message: "creador_email is already registered",
      });
    }

    // Create the InfraestructuraRecurso record (infraestructuraRecurso_id is generated automatically)
    const newUsuario = await Usuario.create({
      nombre: nombre,
      email: email,
    });

    if (newUsuario) {
      //create the user "accesos"
      const newAccesos = await Acceso.create({
        rol: rol,
        recurso: recurso,
        accion: accion,
        usuario_id: newUsuario.usuario_id,
      });
      // Return the created record
      res.status(201).json({ usuario: newUsuario, accesos: newAccesos });
    } else {
      res.status(400).json({
        message: "Error when creating a new usuario and it accesos",
      });
    }
  } catch (error) {
    console.error("Error creating InfraestructuraRecurso:", error);
    res.status(500).json({ message: "Error creating InfraestructuraRecurso" });
  }
};
