import { Model, DataTypes } from "sequelize";
import sequelize from "../../database/database.js";
import Acceso from "./acceso.js";

class Usuario extends Model {}

Usuario.init(
  {
    usuario_id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    nombre: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
  },
  {
    sequelize,
    modelName: "Usuario",
    freezeTableName: true,
  }
);

Usuario.hasMany(Acceso, { as: "accesos", foreignKey: "usuario_id" });
Acceso.belongsTo(Usuario, { foreignKey: "usuario_id" });

export default Usuario;
