import Usuario from "./models/Usuario/usuario.js";
import Acceso from "./models/Usuario/acceso.js";
import Notificacion from "./models/Usuario/notificacion.js";
import InfraestructuraRecurso from "./models/Oti/InfraestructuraRecurso.js";
import Lista_Roles from "./models/Mantenedores/lista_roles.js";
import Lista_Recursos from "./models/Mantenedores/lista_recursos.js";
import Lista_Acciones from "./models/Mantenedores/lista_acciones.js";
import Lista_Comunas_OTI from "./models/Mantenedores/lista_comunas_oti.js";
import Lista_Areas_Institucionales from "./models/Mantenedores/lista_areas_institucionales.js";
import OA_SIES from "./models/Oferta-Academica-Reporte-Sies/OA_SIES.js";
import Reglas_OA_SIES from "./models/Mantenedores/reglas_OA_Reporte_Sies.js";
import MU_postgrado_postitulo_anio_proceso from "./models/Matricula-Unificada/postgrado-postitulo/mu_postgrado_postitulo_anio_proceso.js";
import MU_pregrado_anio_proceso from "./models/Matricula-Unificada/pregrado/mu_pregrado_anio_proceso.js";
import MU_postgrado_postitulo_estudiante from "./models/Matricula-Unificada/postgrado-postitulo/mu_postgrado_postitulo_anio_proceso.js";
import MU_pregrado_estudiante from "./models/Matricula-Unificada/pregrado/mu_pregrado_estudiante.js";
import {
  rulesPregrado,
  rulesPosgrado,
  rulesPostitulo,
} from "./seeders/bulkCreate_Etapa_1_reglas_OA.js";
import {
  rulesPregrado2,
  rulesPosgrado2,
  rulesPostitulo2,
} from "./seeders/bulkCreate_Etapa_2_reglas_OA.js";
import {
  rulesPregrado3,
  rulesPosgrado3,
  rulesPostitulo3,
} from "./seeders/bulkCreate_Etapa_3_reglas_OA.js";
import { server } from "./app.js"; // Import HTTP server
import { sequelize } from "./database/database.js";

async function main() {
  try {
    await sequelize.query('CREATE SCHEMA IF NOT EXISTS oferta_academica');
    await sequelize.sync({ force: false });

    await Reglas_OA_SIES.bulkCreate(rulesPregrado, {
      ignoreDuplicates: true,
    });

    await Reglas_OA_SIES.bulkCreate(rulesPosgrado, {
      ignoreDuplicates: true,
    });

    await Reglas_OA_SIES.bulkCreate(rulesPostitulo, {
      ignoreDuplicates: true,
    });
    await Reglas_OA_SIES.bulkCreate(rulesPregrado2, {
      ignoreDuplicates: true,
    });

    await Reglas_OA_SIES.bulkCreate(rulesPosgrado2, {
      ignoreDuplicates: true,
    });

    await Reglas_OA_SIES.bulkCreate(rulesPostitulo2, {
      ignoreDuplicates: true,
    });
    await Reglas_OA_SIES.bulkCreate(rulesPregrado3, {
      ignoreDuplicates: true,
    });

    await Reglas_OA_SIES.bulkCreate(rulesPosgrado3, {
      ignoreDuplicates: true,
    });

    await Reglas_OA_SIES.bulkCreate(rulesPostitulo3, {
      ignoreDuplicates: true,
    });
    server.listen(3001 || 3002, () => {
      console.log("Server is running on port 3001");
    });
    server.on("error", (error) =>
      console.error("Server could not be started:", error)
    );
  } catch (error) {
    console.error("Unable to synchronize the database:", error);
  }
}

main();
