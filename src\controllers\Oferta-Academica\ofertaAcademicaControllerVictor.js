import { Op } from "sequelize";
import OA_SIES from "../../models/Oferta-Academica-Reporte-Sies/OA_SIES.js";
import ProcesoSies from "../../models/Oferta-Academica-Reporte-Sies/proceso_sies.js";
import <PERSON> from "papaparse";
import { sequelize } from "../../database/database.js";
import { Sequelize } from "sequelize";
import { checkTokenLocal, getEmailFromToken } from "../../auth/auth.js";
import Usuario from "../../models/Usuario/usuario.js";


const checkDatabase = async () => {
  try {
    console.log("Checking database connection...");
    await sequelize.authenticate();
    console.log("Database is connected successfully.");
  } catch (error) {
    console.error("Database connection error:", error);
    return res.status(400).json({ message: "Database not connected" });
  }
};

/**
 * Handler to download csv for "oferta academica"
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const getCsvOacademicas = async (req, res) => {
  try {
    const oa_sies = await OA_SIES.findAll({
      where: {},
      attributes: [
        "cod_sede",
        "nombre_sede",
        "cod_carrera",
        "nombre_carrera",
        "modalidad",
        "cod_jornada",
        "version",
        "cod_tipo_plan_carrera",
        "caracteristicas_tipo_plan",
        "duracion_estudios",
        "duracion_titulacion",
        "duracion_total",
        "regimen",
        "duracion_regimen",
        "nombre_titulo",
        "nombre_grado",
        "cod_nivel_global",
        "cod_nivel_carrera",
        "cod_demre",
        "anio_inicio",
        "acreditacion",
        "elegible_beca_pedagogia",
        "ped_med_odont_otro",
        "requisito_ingreso",
        "semestres_reconocidos",
        "area_actual",
        "area_destino_agricultura",
        "area_destino_ciencias",
        "area_destino_cs_sociales",
        "area_destino_educacion",
        "area_destino_humanidades",
        "area_destino_ingenieria",
        "area_destino_salud",
        "area_destino_servicios",
        "ponderacion_nem",
        "ponderacion_ranking",
        "ponderacion_c_lectora",
        "ponderacion_matematica_1",
        "ponderacion_matematica_2",
        "ponderacion_historia",
        "ponderacion_ciencias",
        "ponderacion_otros",
        "vacantes_primer_semestre",
        "vacantes_segundo_semestre",
        "vacantes_pace",
        "malla_curricular",
        "perfil_egreso",
        "texto_requerido_ingreso",
        "otros_requisitos",
        "mail_difusion_carrera",
        "formato_valor",
        "valor_matricula_anual",
        "costo_titulacion",
        "valor_certificado_diploma",
        "arancel_anual",
        "vigencia_carrera",
      ],
    });
    // Convierte los datos a formato JSON (si es necesario)
    const jsonData = oa_sies.map((item) => item.toJSON());
    // Convierte el JSON a CSV utilizando PapaParse
    const csv = Papa.unparse(jsonData);

    // Establece los encabezados para que el navegador descargue el archivo CSV
    res.header("Content-Type", "text/csv");
    res.attachment("data.csv");
    res.send(csv);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Error generando el archivo CSV" });
  }
};

/**
 * Handler to fetch a all the "Oferta Academica"
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const getOacademicas = async (req, res) => {
  try {
    const fechaActual = new Date();
    const anioActual = fechaActual.getFullYear();

    const oa_sies = await OA_SIES.findAll({
      where: {},
      attributes: ["oa_sies_id", "codigo_unico", "nombre_carrera"],
      include: [
        {
          model: ProcesoSies,
          where: {
            anio_proceso: anioActual, // Año del proceso
            fecha_inicio: {
              [Op.lte]: fechaActual, // Inicio antes o igual a la fecha actual
            },
            fecha_termino: {
              [Op.gte]: fechaActual, // Término después o igual a la fecha actual
            },
          },
          required: true, // Asegura que solo incluya OA_SIES con un ProcesoSies relacionado
        },
      ],
    });
    // const matriculasMapped = carreras.map(mat => ({
    //     carrera_id: mat.carrera_id,
    //     cod_sede: mat['Sede.codigo_sede'],
    //     nombre_sede: mat['Sede.nombre_sede'],
    //     nombre_carrera: mat['nombre_carrera'],
    //     cod_demre: mat['demre'],
    //     semestres_reconocidos: mat['semestres_reconocidos'],
    // }));
    // Return responseData with filtered values
    res.status(200).send(oa_sies);
  } catch (error) {
    console.error("Error fetching Oferta Academica:", error);
    res.status(500).send("Error fetching Oferta Academica");
  }
};
/**
 * Handler to fetch a all the "Oferta Academica Historicos"
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const getOacademicasHistorico = async (req, res) => {
  try {
    const { etapaId } = req.params;

    const oa_sies = await OA_SIES.findAll({
      where: { proceso_sies_id: etapaId },
      attributes: ["oa_sies_id", "codigo_unico", "nombre_carrera"],
    });
    // const matriculasMapped = carreras.map(mat => ({
    //     carrera_id: mat.carrera_id,
    //     cod_sede: mat['Sede.codigo_sede'],
    //     nombre_sede: mat['Sede.nombre_sede'],
    //     nombre_carrera: mat['nombre_carrera'],
    //     cod_demre: mat['demre'],
    //     semestres_reconocidos: mat['semestres_reconocidos'],
    // }));
    // Return responseData with filtered values
    res.status(200).send(oa_sies);
  } catch (error) {
    console.error("Error fetching Oferta Academica Historico:", error);
    res.status(500).send("Error fetching Oferta Academica Historico");
  }
};

/**
 * Handler to fetch a specific Oferta Academica by id.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const getOAcademicaById = async (req, res) => {
  try {
    // Get the ID of the career from the request parameters
    const { oa_sies_id } = req.params;
    console.log(oa_sies_id);
    // Find a single record of the career with the provided ID
    const oa_sies = await OA_SIES.findOne({
      where: { oa_sies_id: oa_sies_id },
      attributes: [
        "oa_sies_id",
        "cod_sede",
        "nombre_sede",
        "cod_carrera",
        "nombre_carrera",
        "modalidad",
        "cod_jornada",
        "version",
        "cod_tipo_plan_carrera",
        "caracteristicas_tipo_plan",
        "duracion_estudios",
        "duracion_titulacion",
        "duracion_total",
        "regimen",
        "duracion_regimen",
        "nombre_titulo",
        "nombre_grado",
        "cod_nivel_global",
        "cod_nivel_carrera",
        "cod_demre",
        "anio_inicio",
        "acreditacion",
        "elegible_beca_pedagogia",
        "ped_med_odont_otro",
        "requisito_ingreso",
        "semestres_reconocidos",
        "area_actual",
        "area_destino_agricultura",
        "area_destino_ciencias",
        "area_destino_cs_sociales",
        "area_destino_educacion",
        "area_destino_humanidades",
        "area_destino_ingenieria",
        "area_destino_salud",
        "area_destino_servicios",
        "ponderacion_nem",
        "ponderacion_ranking",
        "ponderacion_c_lectora",
        "ponderacion_matematica_1",
        "ponderacion_matematica_2",
        "ponderacion_historia",
        "ponderacion_ciencias",
        "ponderacion_otros",
        "vacantes_primer_semestre",
        "vacantes_segundo_semestre",
        "vacantes_pace",
        "malla_curricular",
        "perfil_egreso",
        "texto_requerido_ingreso",
        "otros_requisitos",
        "mail_difusion_carrera",
        "formato_valor",
        "valor_matricula_anual",
        "costo_titulacion",
        "valor_certificado_diploma",
        "arancel_anual",
        "vigencia_carrera",
      ],
      raw: true,
    });

    // If the career is not found with the given ID, return a 404 error
    if (!oa_sies) {
      return res.status(404).send("Carrera not found");
    }
    
    
    // Map the result to return the desired format
    // const oa_siesMapped = {
      //     carrera_id: oa_sies.carrera_id,
      //     cod_sede: oa_sies['Sede.codigo_sede'],
    //     nombre_sede: oa_sies['Sede.nombre_sede'],
    //     cod_carrera: oa_sies.codigo_unico,
    //     nombre_carrera: oa_sies.nombre_carrera,
    //     modalidad: oa_sies['Sies.modalidad_sies'],
    //     cod_jornada: oa_sies['Sies.jornada_sies'],
    //     version: oa_sies['Sies.version_sies'],
    //     cod_tipo_plan_carrera: '', // Información no especificada
    //     caracteristicas_tipo_plan: '', // Información no especificada
    //     duracion_estudios: oa_sies['Duracion.duracion_estudios'],
    //     duracion_titulacion: oa_sies['Duracion.duracion_titulacion'],
    //     duracion_total: oa_sies['Duracion.duracion_total'],
    //     regimen: oa_sies['Duracion.regimen'],
    //     duracion_regimen: oa_sies['Duracion.duracion_formal_regimen'],
    //     nombre_titulo: oa_sies['PerfilProfesional.nombre_titulo'],
    //     nombre_grado: oa_sies['PerfilProfesional.grado_academico'],
    //     cod_nivel_global: oa_sies.nivel_global,
    //     cod_nivel_carrera: oa_sies.nivel_carrera,
    //     cod_demre: oa_sies.demre,
    //     anio_inicio: oa_sies.anio_inicio,
    //     acreditacion: oa_sies.acreditacion,
    //     elegible_beca_pedagogia: oa_sies.elegibilidad_beca_pedagogia,
    //     ped_med_odont_otro: oa_sies.pedagogia_medicina_otro,
    //     requisito_ingreso: oa_sies.requisito_ingreso,
    //     semestres_reconocidos: oa_sies.semestres_reconocidos,
    //     area_actual: oa_sies['Area.area_actual'],
    //     area_destino_agricultura: oa_sies['Area.area_dest_agricultura'],
    //     area_destino_ciencias: oa_sies['Area.area_dest_ciencias'],
    //     area_destino_cs_sociales: oa_sies.area_destino_cs_sociales,
    //     area_destino_educacion: oa_sies.area_destino_educacion,
    //     area_destino_humanidades: oa_sies.area_destino_humanidades,
    //     area_destino_ingenieria: oa_sies.area_destino_ingenieria,
    //     area_destino_salud: oa_sies.area_destino_salud,
    //     area_destino_servicios: oa_sies.area_destino_servicios,
    //     ponderacion_nem: oa_sies.ponderacion_nem,
    //     ponderacion_ranking: oa_sies.ponderacion_ranking,
    //     ponderacion_c_lectora: oa_sies.ponderacion_c_lectora,
    //     ponderacion_matematicas: oa_sies.ponderacion_matematicas,
    //     ponderacion_matematicas_2: oa_sies.ponderacion_matematicas_2,
    //     ponderacion_historia: oa_sies.ponderacion_historia,
    //     ponderacion_ciencias: oa_sies.ponderacion_ciencias,
    //     ponderacion_otros: oa_sies.ponderacion_otros,
    //     vacantes_primer_semestre: oa_sies.vacantes_primer_semestre,
    //     vacantes_segundo_semestre: oa_sies.vacantes_segundo_semestre,
    //     vacantes_pace: oa_sies.vacantes_pace,
    //     malla_curricular: oa_sies.malla_curricular,
    //     perfil_egreso: oa_sies.perfil_egreso,
    //     texto_requisito_ingreso: oa_sies.texto_requisito_ingreso,
    //     otros_requisitos: oa_sies.otros_requisitos,
    //     mail_difusion_carrera: oa_sies.mail_difusion_carrera,
    //     formato_valor: oa_sies.formato_valor,
    //     valor_matricula_anual: oa_sies.valor_matricula_anual,
    //     costo_titulacion: oa_sies.costo_titulacion,
    //     valor_certificado_diploma: oa_sies.valor_certificado_diploma,
    //     arancel_anual: oa_sies.arancel_anual,
    //     vigencia_carrera: oa_sies.vigencia,
    // };

    // Send the mapped object as a response
    //res.status(200).send(oa_siesMapped);
    res.status(200).send(oa_sies);
  } catch (error) {
    console.error("Error fetching Oferta Académica:", error);
    res.status(500).send("Error fetching Oferta Académica");
  }
};


/**
 * Edit a specific oferta academidca by id.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const updateOAcademica = async (req, res) => {
  try {
    var {
      oa_sies_id,
      cod_sede,
      nombre_sede,
      cod_carrera,
      nombre_carrera,
      modalidad,
      cod_jornada,
      version,
      cod_tipo_plan_carrera,
      caracteristicas_tipo_plan,
      duracion_estudios,
      duracion_titulacion,
      duracion_total,
      regimen,
      duracion_regimen,
      nombre_titulo,
      nombre_grado,
      cod_nivel_global,
      cod_nivel_carrera,
      cod_demre,
      anio_inicio,
      acreditacion,
      elegible_beca_pedagogia,
      ped_med_odont_otro,
      requisito_ingreso,
      semestres_reconocidos,
      area_actual,
      area_destino_agricultura,
      area_destino_ciencias,
      area_destino_cs_sociales,
      area_destino_educacion,
      area_destino_humanidades,
      area_destino_ingenieria,
      area_destino_salud,
      area_destino_servicios,
      ponderacion_nem,
      ponderacion_ranking,
      ponderacion_c_lectora,
      ponderacion_matematica_1,
      ponderacion_matematica_2,
      ponderacion_historia,
      ponderacion_ciencias,
      ponderacion_otros,
      vacantes_primer_semestre,
      vacantes_segundo_semestre,
      vacantes_pace,
      malla_curricular,
      perfil_egreso,
      texto_requerido_ingreso,
      otros_requisitos,
      mail_difusion_carrera,
      formato_valor,
      valor_matricula_anual,
      costo_titulacion,
      valor_certificado_diploma,
      arancel_anual,
      vigencia_carrera,
    } = req.body;

    const oAcademicaSies = await OA_SIES.findByPk(oa_sies_id);
    if (!oAcademicaSies) {
      return res
        .status(404)
        .json({ message: "Oferta Academica no encontrada" });
    }
    await oAcademicaSies.update({
      cod_sede: cod_sede,
      nombre_sede: nombre_sede,
      cod_carrera: cod_carrera,
      nombre_carrera: nombre_carrera,
      modalidad: modalidad,
      cod_jornada: cod_jornada,
      version: version,
      cod_tipo_plan_carrera: cod_tipo_plan_carrera,
      caracteristicas_tipo_plan: caracteristicas_tipo_plan,
      duracion_estudios: duracion_estudios,
      duracion_titulacion: duracion_titulacion,
      duracion_total: duracion_total,
      regimen: regimen,
      duracion_regimen: duracion_regimen,
      nombre_titulo: nombre_titulo,
      nombre_grado: nombre_grado,
      cod_nivel_global: cod_nivel_global,
      cod_nivel_carrera: cod_nivel_carrera,
      cod_demre: cod_demre,
      anio_inicio: anio_inicio,
      acreditacion: acreditacion,
      elegible_beca_pedagogia: elegible_beca_pedagogia,
      ped_med_odont_otro: ped_med_odont_otro,
      requisito_ingreso: requisito_ingreso,
      semestres_reconocidos: semestres_reconocidos,
      area_actual: area_actual,
      area_destino_agricultura: area_destino_agricultura,
      area_destino_ciencias: area_destino_ciencias,
      area_destino_cs_sociales: area_destino_cs_sociales,
      area_destino_educacion: area_destino_educacion,
      area_destino_humanidades: area_destino_humanidades,
      area_destino_ingenieria: area_destino_ingenieria,
      area_destino_salud: area_destino_salud,
      area_destino_servicios: area_destino_servicios,
      ponderacion_nem: ponderacion_nem,
      ponderacion_ranking: ponderacion_ranking,
      ponderacion_c_lectora: ponderacion_c_lectora,
      ponderacion_matematica_1: ponderacion_matematica_1,
      ponderacion_matematica_2: ponderacion_matematica_2,
      ponderacion_historia: ponderacion_historia,
      ponderacion_ciencias: ponderacion_ciencias,
      ponderacion_otros: ponderacion_otros,
      vacantes_primer_semestre: vacantes_primer_semestre,
      vacantes_segundo_semestre: vacantes_segundo_semestre,
      vacantes_pace: vacantes_pace,
      malla_curricular: malla_curricular,
      perfil_egreso: perfil_egreso,
      texto_requerido_ingreso: texto_requerido_ingreso,
      otros_requisitos: otros_requisitos,
      mail_difusion_carrera: mail_difusion_carrera,
      formato_valor: formato_valor,
      valor_matricula_anual: valor_matricula_anual,
      costo_titulacion: costo_titulacion,
      valor_certificado_diploma: valor_certificado_diploma,
      arancel_anual: arancel_anual,
      vigencia_carrera: vigencia_carrera,
    });
    return res.status(200).json(oAcademicaSies);
  } catch (error) {
    console.error("Error fetching Oferta Académica:", error);
    return res.status(500).send({ message: "Error fetching Oferta Académica" });
  }
};

/**
 * Handler to create an "Oferta Academica" in the database.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const postOAcademica = async (req, res) => {
  var {
    etapa_actual,
    anio_a_reportar,
    autor,
    cantidad_matricula_dfe,
    cantidad_beneficiado_dfe,
    codigo_unico,
    cod_sede,
    nombre_sede,
    cod_carrera,
    nombre_carrera,
    modalidad,
    cod_jornada,
    version,
    cod_tipo_plan_carrera,
    caracteristicas_tipo_plan,
    duracion_estudios,
    duracion_titulacion,
    duracion_total,
    regimen,
    duracion_regimen,
    nombre_titulo,
    nombre_grado,
    cod_nivel_global,
    cod_nivel_carrera,
    cod_demre,
    anio_inicio,
    acreditacion,
    elegible_beca_pedagogia,
    ped_med_odont_otro,
    requisito_ingreso,
    semestres_reconocidos,
    area_actual,
    area_destino_agricultura,
    area_destino_ciencias,
    area_destino_cs_sociales,
    area_destino_educacion,
    area_destino_humanidades,
    area_destino_ingenieria,
    area_destino_salud,
    area_destino_servicios,
    ponderacion_nem,
    ponderacion_ranking,
    ponderacion_c_lectora,
    ponderacion_matematica_1,
    ponderacion_matematica_2,
    ponderacion_historia,
    ponderacion_ciencias,
    ponderacion_otros,
    vacantes_primer_semestre,
    vacantes_segundo_semestre,
    vacantes_pace,
    malla_curricular,
    perfil_egreso,
    texto_requerido_ingreso,
    otros_requisitos,
    mail_difusion_carrera,
    formato_valor,
    valor_matricula_anual,
    costo_titulacion,
    valor_certificado_diploma,
    arancel_anual,
    vigencia_carrera,
    proceso_sies_id,
  } = req.body;

  try {
    const newOAcademica = await OA_SIES.create({
      etapa_actual: etapa_actual,
      anio_a_reportar: anio_a_reportar,
      autor: autor,
      cantidad_matricula_dfe: cantidad_matricula_dfe,
      cantidad_beneficiado_dfe: cantidad_beneficiado_dfe,
      codigo_unico: codigo_unico,
      cod_sede: cod_sede,
      nombre_sede: nombre_sede,
      cod_carrera: cod_carrera,
      nombre_carrera: nombre_carrera,
      modalidad: modalidad,
      cod_jornada: cod_jornada,
      version: version,
      cod_tipo_plan_carrera: cod_tipo_plan_carrera,
      caracteristicas_tipo_plan: caracteristicas_tipo_plan,
      duracion_estudios: duracion_estudios,
      duracion_titulacion: duracion_titulacion,
      duracion_total: duracion_total,
      regimen: regimen,
      duracion_regimen: duracion_regimen,
      nombre_titulo: nombre_titulo,
      nombre_grado: nombre_grado,
      cod_nivel_global: cod_nivel_global,
      cod_nivel_carrera: cod_nivel_carrera,
      cod_demre: cod_demre,
      anio_inicio: anio_inicio,
      acreditacion: acreditacion,
      elegible_beca_pedagogia: elegible_beca_pedagogia,
      ped_med_odont_otro: ped_med_odont_otro,
      requisito_ingreso: requisito_ingreso,
      semestres_reconocidos: semestres_reconocidos,
      area_actual: area_actual,
      area_destino_agricultura: area_destino_agricultura,
      area_destino_ciencias: area_destino_ciencias,
      area_destino_cs_sociales: area_destino_cs_sociales,
      area_destino_educacion: area_destino_educacion,
      area_destino_humanidades: area_destino_humanidades,
      area_destino_ingenieria: area_destino_ingenieria,
      area_destino_salud: area_destino_salud,
      area_destino_servicios: area_destino_servicios,
      ponderacion_nem: ponderacion_nem,
      ponderacion_ranking: ponderacion_ranking,
      ponderacion_c_lectora: ponderacion_c_lectora,
      ponderacion_matematica_1: ponderacion_matematica_1,
      ponderacion_matematica_2: ponderacion_matematica_2,
      ponderacion_historia: ponderacion_historia,
      ponderacion_ciencias: ponderacion_ciencias,
      ponderacion_otros: ponderacion_otros,
      vacantes_primer_semestre: vacantes_primer_semestre,
      vacantes_segundo_semestre: vacantes_segundo_semestre,
      vacantes_pace: vacantes_pace,
      malla_curricular: malla_curricular,
      perfil_egreso: perfil_egreso,
      texto_requerido_ingreso: texto_requerido_ingreso,
      otros_requisitos: otros_requisitos,
      mail_difusion_carrera: mail_difusion_carrera,
      formato_valor: formato_valor,
      valor_matricula_anual: valor_matricula_anual,
      costo_titulacion: costo_titulacion,
      valor_certificado_diploma: valor_certificado_diploma,
      arancel_anual: arancel_anual,
      vigencia_carrera: vigencia_carrera,
      proceso_sies_id: proceso_sies_id,
    });
    return res.status(200).json(newOAcademica);
  } catch (error) {
    console.error("Error creating Oferta Academica:", error);
    return res.status(500).json({ message: "Error creating Oferta Academica" });
  }
}; 

export const updateOacademicaEtapa1Pregrado = async (req, res) => {
  try {
    const {
      oa_sies_id,
      acreditacion,
      elegible_beca_pedagogia,
      requisito_ingreso,
      semestres_reconocidos,
      area_destino_agricultura,
      area_destino_ciencias,
      area_destino_cs_sociales,
      area_destino_educacion,
      area_destino_humanidades,
      area_destino_ingenieria,
      area_destino_salud,
      area_destino_servicios,
      malla_curricular,
      mail_difusion_carrera,
    } = req.body;
    

    const oAcademicaSies = await OA_SIES.findByPk(oa_sies_id);
    if (!oAcademicaSies) {
      return res
        .status(404)
        .json({ message: "Oferta Academica no encontrada" });
    }
    await oAcademicaSies.update({
      acreditacion: acreditacion,
      elegible_beca_pedagogia: elegible_beca_pedagogia,
      requisito_ingreso: requisito_ingreso,
      semestres_reconocidos: semestres_reconocidos,
      area_destino_agricultura: area_destino_agricultura,
      area_destino_ciencias: area_destino_ciencias,
      area_destino_cs_sociales: area_destino_cs_sociales,
      area_destino_educacion: area_destino_educacion,
      area_destino_humanidades: area_destino_humanidades,
      area_destino_ingenieria: area_destino_ingenieria,
      area_destino_salud: area_destino_salud,
      area_destino_servicios: area_destino_servicios,
      malla_curricular: malla_curricular,
      mail_difusion_carrera: mail_difusion_carrera,
    });
    return res.status(200).json(oAcademicaSies);
  } catch (error) {
    console.error("Error fetching Oferta Académica:", error);
    return res.status(500).send({ message: "Error fetching Oferta Académica" });
  }
};
export const updateOacademicaEtapa1PosgradoPostitulo = async (req, res) => {
  try {
    const {
      oa_sies_id,
      regimen,
      duracion_regimen,
      cod_demre,
      acreditacion,
      elegible_beca_pedagogia,
      requisito_ingreso,
      semestres_reconocidos,
      area_destino_agricultura,
      area_destino_ciencias,
      area_destino_cs_sociales,
      area_destino_educacion,
      area_destino_humanidades,
      area_destino_ingenieria,
      area_destino_salud,
      area_destino_servicios,
      ponderacion_nem,
      ponderacion_ranking,
      ponderacion_c_lectora,
      ponderacion_matematica_1,
      ponderacion_matematica_2,
      ponderacion_historia,
      ponderacion_ciencias,
      ponderacion_otros,
      vacantes_pace,
      malla_curricular,
      perfil_egreso,
      texto_requerido_ingreso,
      otros_requisitos,
      mail_difusion_carrera,
      vigencia_carrera
    } = req.body;

    const oAcademicaPosgrado = await OA_SIES.findByPk(oa_sies_id);
    if (!oAcademicaPosgrado) {
      return res
        .status(404)
        .json({ message: "Oferta Académica Posgrado no encontrada" });
    }

    await oAcademicaPosgrado.update({
      regimen,
      duracion_regimen,
      cod_demre,
      acreditacion,
      elegible_beca_pedagogia,
      requisito_ingreso,
      semestres_reconocidos,
      area_destino_agricultura,
      area_destino_ciencias,
      area_destino_cs_sociales,
      area_destino_educacion,
      area_destino_humanidades,
      area_destino_ingenieria,
      area_destino_salud,
      area_destino_servicios,
      ponderacion_nem,
      ponderacion_ranking,
      ponderacion_c_lectora,
      ponderacion_matematica_1,
      ponderacion_matematica_2,
      ponderacion_historia,
      ponderacion_ciencias,
      ponderacion_otros,
      vacantes_pace,
      malla_curricular,
      perfil_egreso,
      texto_requerido_ingreso,
      otros_requisitos,
      mail_difusion_carrera,
      vigencia_carrera
    });

    return res.status(200).json(oAcademicaPosgrado);
  } catch (error) {
    console.error("Error updating Oferta Académica Posgrado:", error);
    return res.status(500).send({ message: "Error updating Oferta Académica Posgrado" });
  }
};

export const updateOacademicaEtapa3Pregrado = async (req, res) => {
  try {
    const {
      oa_sies_id,
      acreditacion,
      malla_curricular,
      mail_difusion_carrera,
      formato_valor,
      valor_matricula_anual,
      costo_titulacion,
      valor_certificado_diploma,
      arancel_anual,
    } = req.body;

    const oAcademicaPregrado = await OA_SIES.findByPk(oa_sies_id);
    if (!oAcademicaPregrado) {
      return res.status(404).json({ message: "Oferta Académica Etapa 3 Pregrado no encontrada" });
    }

    await oAcademicaPregrado.update({
      acreditacion,
      malla_curricular,
      mail_difusion_carrera,
      formato_valor,
      valor_matricula_anual,
      costo_titulacion,
      valor_certificado_diploma,
      arancel_anual,
    });

    return res.status(200).json(oAcademicaPregrado);
  } catch (error) {
    console.error("Error updating Oferta Académica Etapa 3 Pregrado:", error);
    return res.status(500).json({ message: "Error updating Oferta Académica Etapa 3 Pregrado" });
  }
};
//

//David Meza

/**
 * Handler to fetch Oferta Academica by proceso_sies_id.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const getOAcademicaByProcesoSiesId = async (req, res) => {
  try {
    const { proceso_sies_id } = req.params;

    const oa_sies = await OA_SIES.findAll({
      where: { proceso_sies_id },
      attributes: [
        "oa_sies_id",
        "cod_sede",
        "nombre_sede",
        "cod_carrera",
        "nombre_carrera",
        "modalidad",
        "cod_jornada",
        "version",
        "cod_tipo_plan_carrera",
        "caracteristicas_tipo_plan",
        "duracion_estudios",
        "duracion_titulacion",
        "duracion_total",
        "regimen",
        "duracion_regimen",
        "nombre_titulo",
        "nombre_grado",
        "cod_nivel_global",
        "cod_nivel_carrera",
        "cod_demre",
        "anio_inicio",
        "acreditacion",
        "elegible_beca_pedagogia",
        "ped_med_odont_otro",
        "requisito_ingreso",
        "semestres_reconocidos",
        "area_actual",
        "area_destino_agricultura",
        "area_destino_ciencias",
        "area_destino_cs_sociales",
        "area_destino_educacion",
        "area_destino_humanidades",
        "area_destino_ingenieria",
        "area_destino_salud",
        "area_destino_servicios",
        "ponderacion_nem",
        "ponderacion_ranking",
        "ponderacion_c_lectora",
        "ponderacion_matematica_1",
        "ponderacion_matematica_2",
        "ponderacion_historia",
        "ponderacion_ciencias",
        "ponderacion_otros",
        "vacantes_primer_semestre",
        "vacantes_segundo_semestre",
        "vacantes_pace",
        "malla_curricular",
        "perfil_egreso",
        "texto_requerido_ingreso",
        "otros_requisitos",
        "mail_difusion_carrera",
        "formato_valor",
        "valor_matricula_anual",
        "costo_titulacion",
        "valor_certificado_diploma",
        "arancel_anual",
        "vigencia_carrera",
      ],
    });

    if (!oa_sies || oa_sies.length === 0) {
      return res.status(404).json({ message: "No Oferta Academica found for the given proceso_sies_id" });
    }

    res.status(200).json(oa_sies);
  } catch (error) {
    console.error("Error fetching Oferta Academica by proceso_sies_id:", error);
    res.status(500).json({ message: "Error fetching Oferta Academica by proceso_sies_id" });
  }
};

/**
 * Handler to get the count of "Oferta Academica" per etapa for a given proceso_sies_id.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const getCantCarrerasPerEtapa = async (req, res) => {
  try {
    const { proceso_sies_id } = req.params;

    const etapasCount = await OA_SIES.findAll({
      where: { proceso_sies_id },
      attributes: [
        "etapa_actual",
        [Sequelize.fn("COUNT", Sequelize.col("oa_sies_id")), "cantidad_carreras"],
      ],
      group: ["etapa_actual"],
    });

    res.status(200).json(etapasCount);
  } catch (error) {
    console.error("Error fetching count of carreras per etapa:", error);
    res.status(500).json({ message: "Error fetching count of carreras per etapa" });
  }
};
//david meza
export const postOAcademicaFromCSV = async (req, res) => {
  const userToken = req.headers.authorization;
  const { proceso_sies_id, etapa_actual } = req.params;
  const { carreras } = req.body; // Assuming the CSV data is sent in the request body
  try {
    // console.log('Archivo recibido:', req.file);
    // console.log('Body:', req.body);
    // console.log("carreras");
    console.log("\n\n\n"); // Adding line breaks for better readability
    console.log("\n\n\n"); // Adding line breaks for better readability
    console.log("\n\n\n"); // Adding line breaks for better readability
    console.time("Validation Time");
    console.log(proceso_sies_id);
    console.log(etapa_actual);
    //console.log(carreras);
    // Check the database connection
    await checkDatabase();
    // check if the token is valid and not expired
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    const creador_email = await getEmailFromToken(userToken);
    const creador = await Usuario.findOne({
      where: { email: { [Op.iLike]: creador_email } },
    });
    if (!creador) {
      return res.status(404).json({ message: "Usuario not found" });
    }


    console.log(validateCarreras(carreras));

    // Convert column names to lowercase

    const carrerasLowerCase = carreras.map((carrera) => {
      const carreraLowerCase = {};
      for (const key in carrera) {
        carreraLowerCase[key.toLowerCase()] = carrera[key];
      }
      return carreraLowerCase;
    });
    // console.log(carrerasLowerCase);
    // Iterate over the carreras array and create each record in the database
    // for (const carrera of carrerasLowerCase) {
    //   await OA_SIES.create({
    //     ...carrera,
    //     proceso_sies_id,
    //     etapa_actual,
    //     creador_id: creador.usuario_id,
    //   });
    // }
    // return res.status(200).json({ message: "All correct" });
    return res.status(201).json({ message: "All correct" });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};


const validateCarreras = (carreras) => {
  const errors = [];
  carreras.forEach((carrera, index) => {



    // PONDERACION_MATEMATICAS_2
    if (carrera.PONDERACION_MATEMATICAS_2 < 0 || carrera.PONDERACION_MATEMATICAS_2 > 100) {
      errors.push(
      `Error en la columna ${index + 2}: "PONDERACION_MATEMATICAS_2" no puede ser menor a 0 ni mayor a 100%.`
      );
    }

    // VACANTES_SEGUNDO_SEMESTRE
    if (carrera.VACANTES_SEGUNDO_SEMESTRE == null) {
      errors.push(
      `Error en la columna ${index + 2}: "VACANTES_SEGUNDO_SEMESTRE" no puede ser nulo. En caso de no corresponder, debe completar con "0".`
      );
    }

    // VACANTES_PRIMER_SEMESTRE
    if (carrera.VACANTES_PRIMER_SEMESTRE == null) {
      errors.push(
      `Error en la columna ${index + 2}: "VACANTES_PRIMER_SEMESTRE" no puede ser nulo. En caso de no corresponder, debe completar con "0".`
      );
    }

    // PONDERACION_C_LECTORA
    if (carrera.PONDERACION_C_LECTORA == null) {
      errors.push(
      `Error en la columna ${index + 2}: "PONDERACION_C_LECTORA" no puede ser nulo. En caso de no corresponder, debe completar con "0".`
      );
    }

    // PONDERACION_MATEMATICAS
    if (carrera.PONDERACION_MATEMATICAS == null) {
      errors.push(
      `Error en la columna ${index + 2}: "PONDERACION_MATEMATICAS" no puede ser nulo. En caso de no corresponder, debe completar con "0".`
      );
    }

    // PONDERACION_OTROS
    if (carrera.PONDERACION_OTROS == null) {
      errors.push(
      `Error en la columna ${index + 2}: "PONDERACION_OTROS" no puede ser nulo. En caso de no corresponder, debe completar con "0".`
      );
    }

    // PONDERACION_NEM
    if (carrera.PONDERACION_NEM == null) {
      errors.push(
      `Error en la columna ${index + 2}: "PONDERACION_NEM" no puede ser nulo. En caso de no corresponder, debe completar con "0".`
      );
    } else if (carrera.PONDERACION_NEM < 0 || carrera.PONDERACION_NEM > 100) {
      errors.push(
      `Error en la columna ${index + 2}: "PONDERACION_NEM" no puede ser menor a 0 ni mayor a 100%.`
      );
    }

    // PONDERACION_RANKING
    if (carrera.PONDERACION_RANKING == null) {
      errors.push(
      `Error en la columna ${index + 2}: "PONDERACION_RANKING" no puede ser nulo. En caso de no corresponder, debe completar con "0".`
      );
    } else if (carrera.PONDERACION_RANKING < 0 || carrera.PONDERACION_RANKING > 100) {
      errors.push(
      `Error en la columna ${index + 2}: "PONDERACION_RANKING" no puede ser menor a 0 ni mayor a 100%.`
      );
    }

    // PONDERACION_HISTORIA
    if (carrera.PONDERACION_HISTORIA == null) {
      errors.push(
      `Error en la columna ${index + 2}: "PONDERACION_HISTORIA" no puede ser nulo. En caso de no corresponder, debe completar con "0".`
      );
    }

    // PONDERACION_CIENCIAS
    if (carrera.PONDERACION_CIENCIAS == null) {
      errors.push(
      `Error en la columna ${index + 2}: "PONDERACION_CIENCIAS" no puede ser nulo. En caso de no corresponder, debe completar con "0".`
      );
    }

    // VACANTES_PACE
    if (carrera.VACANTES_PACE == null) {
      errors.push(
      `Error en la columna ${index + 2}: "VACANTES_PACE" no puede ser nulo. En caso de no corresponder, debe completar con "0".`
      );
    } else if (carrera.VACANTES_PACE < 0) {
      errors.push(
      `Error en la columna ${index + 2}: "VACANTES_PACE" debe ser mayor o igual a 0.`
      );
    }

    // DURACION_REGIMEN
    if (carrera.DURACION_REGIMEN == null) {
      errors.push(
      `Error en la columna ${index + 2}: "DURACION_REGIMEN" no puede ser nulo.`
      );
    }

    // COD_DEMRE
    if (carrera.COD_DEMRE == null) {
      errors.push(
      `Error en la columna ${index + 2}: "COD_DEMRE" no puede ser nulo. En caso de no corresponder, debe completar con "0".`
      );
    }

    // VIGENCIA_CARRERA validations
    if (carrera.VIGENCIA_CARRERA === 1) {
      if (!carrera.MALLA_CURRICULAR) {
      errors.push(
        `Error en la columna ${index + 2}: "MALLA_CURRICULAR" debe completarse cuando "VIGENCIA_CARRERA" es 1.`
      );
      }
      if (!carrera.PERFIL_EGRESO) {
      errors.push(
        `Error en la columna ${index + 2}: "PERFIL_EGRESO" debe completarse cuando "VIGENCIA_CARRERA" es 1.`
      );
      }
      if (!carrera.MAIL_DIFUSION_CARRERA) {
      errors.push(
        `Error en la columna ${index + 2}: "MAIL_DIFUSION_CARRERA" debe completarse cuando "VIGENCIA_CARRERA" es 1.`
      );
      }
    }

    // Ponderaciones sum validation
    const totalPonderaciones =
      Number(carrera.PONDERACION_NEM || 0) +
      Number(carrera.PONDERACION_RANKING || 0) +
      Number(carrera.PONDERACION_C_LECTORA || 0) +
      Number(carrera.PONDERACION_MATEMATICAS || 0) +
      Number(carrera.PONDERACION_MATEMATICAS_2 || 0) +
      Number(carrera.PONDERACION_HISTORIA || 0) +
      Number(carrera.PONDERACION_CIENCIAS || 0) +
      Number(carrera.PONDERACION_OTROS || 0);

    if (totalPonderaciones < 100 || totalPonderaciones > 160) {
      errors.push(
      `Error en la columna ${index + 2}: La suma de las ponderaciones no puede ser menor a 100% ni superar el 160%.`
      );
    }

  });

  return errors.length === 0 ? true : errors;
};
/**
 * Handler to delete "Oferta Academica" by etapa and proceso_sies_id.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const deleteOAcademicaByEtapaAndProceso = async (req, res) => {
  try {
    const { proceso_sies_id, etapa } = req.params;

    const deletedCount = await OA_SIES.destroy({
      where: {
      proceso_sies_id,
      etapa_actual: etapa,
      },
    });

    if (deletedCount === 0) {
      return res.status(404).json({ message: "No records found to delete" });
    }

    res.status(200).json({ message: `${deletedCount} records deleted successfully` });
  } catch (error) {
    console.error("Error deleting Oferta Academica:", error);
    res.status(500).json({ message: "Error deleting Oferta Academica" });
  }
};
