import { checkAtributosNecesarios } from "../../utils/checkAtributosNecesarios.js";
import uuidValidate from "uuid-validate";
import Predio from "../../models/Oti/predio.js";
import { checkDataType } from "../../utils/checkDataType.js";
import Usuario from "../../models/Usuario/usuario.js";
import Acceso from "../../models/Usuario/acceso.js";
import { Op } from "sequelize";
import InfraestructuraRecurso from "../../models/Oti/InfraestructuraRecurso.js";
import { checkTokenLocal } from "../../auth/auth.js";
import validateNumbers from "../../utils/validateNumberEqualGreatZero.js";
import { sequelize } from "../../database/database.js";
import { checkPermisosUsuario } from "../../utils/checkPermisosUsuario.js";

const checkDatabase = async () => {
  try {
    console.log("Checking database connection...");
    await sequelize.authenticate();
    console.log("Database is connected successfully.");
  } catch (error) {
    console.error("Database connection error:", error);
    return res.status(400).json({ message: "Database not connected" });
  }
};

// Fetch a specific predio by predio_id
export const getPredio = async (req, res) => {
  const userToken = req.headers.authorization;
  const { predio_id } = req.params;

  try {
    // Check the database connection
    await checkDatabase();
    if (!uuidValidate(predio_id)) {
      return res.status(400).json({ message: "Invalid predio_id" });
    }
    // check if the token is valid and not expired
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    const predio = await Predio.findByPk(predio_id);
    if (!predio) {
      return res.status(404).json({ message: "Predio not found" });
    }

    res.status(200).json(predio);
  } catch (error) {
    console.error("Error fetching predio:", error);
    res.status(500).json({ message: "Error fetching predio" });
  }
};

// Fetch all predios associated with a specific infraestructuraRecurso_id
export const getAllPredios = async (req, res) => {
  const userToken = req.headers.authorization;
  const { infraestructuraRecurso_id } = req.params;

  try {
    // Check the database connection
    await checkDatabase();
    if (!uuidValidate(infraestructuraRecurso_id)) {
      return res
        .status(400)
        .json({ message: "Invalid infraestructuraRecurso_id" });
    }
    // check if the token is valid and not expired
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }

    const infraestructuraRecurso = await InfraestructuraRecurso.findByPk(
      infraestructuraRecurso_id
    );
    if (!infraestructuraRecurso) {
      return res
        .status(404)
        .json({ message: "InfraestructuraRecurso not found" });
    }

    const predios = await Predio.findAll({
      where: { infraestructuraRecurso_id },
    });

    if (predios.length === 0) {
      return res.status(404).json({ message: "No predios found" });
    }

    res.status(200).json(predios);
  } catch (error) {
    console.error("Error fetching all predios:", error);
    res.status(500).json({ message: "Error fetching predios" });
  }
};

// Create a new predio
export const postPredio = async (req, res) => {
  const {
    tipo_infraestructura,
    nombre_identificacion,
    comuna,
    direccion_inmueble,
    total_hectareas_predio,
    vigencia,
    anio_proceso,
    creador_email,
    creador_id,
    userToken,
  } = req.body;

  // List of required attributes for predio
  const requiredAttributes = [
    "tipo_infraestructura",
    "nombre_identificacion",
    "comuna",
    "direccion_inmueble",
    "total_hectareas_predio",
    "vigencia",
    "anio_proceso",
    "creador_email",
    "creador_id",
    "userToken",
  ];

  try {
    // Check the database connection
    await checkDatabase();
    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredAttributes
    );
    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }
    console.log(vigencia);
    var areNumbersValid = await validateNumbers([
      total_hectareas_predio,
      vigencia,
    ]);
    if (areNumbersValid == false) {
      return res.status(400).json({ message: "Invalid number format" });
    }
    if (vigencia != 0 && vigencia != 1) {
      return res.status(400).json({ message: "Invalid vigencia value" });
    }

    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }

    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: creador_email },
      },
    });

    if (!usuario) {
      return res.status(404).json({
        message:
          "User not found or does not have access to the specified resource",
      });
    }
    // Validate the user's roles
    const validRoles = [
      "Encargado",
      "Usuario General",
      "Administrador Sistema",
    ];

    const hasAccess = await checkPermisosUsuario(
      creador_email,
      validRoles,
      "OTI"
    );
    if (!hasAccess) {
      return res.status(401).json({
        message:
          "Unauthorized access: User does not have the required permissions",
      });
    }

    const infraestructuraRecurso = await InfraestructuraRecurso.findOne({
      where: {
        anio_proceso: anio_proceso,
      },
    });

    if (!infraestructuraRecurso) {
      return res.status(404).json({
        message: "infraestructuraRecurso was not found",
      });
    }
    // If the infarestructuraRecurso is already finalized, then the predio can't be created
    if (infraestructuraRecurso.is_finalized == true) {
      return res.status(403).json({
        message: "Can't create new Predios if is already validated",
      });
    }

    // Create the new predio record
    const newPredio = await Predio.create({
      tipo_infraestructura: 5,
      nombre_identificacion,
      comuna,
      direccion_inmueble,
      total_hectareas_predio,
      vigencia,
      anio_proceso,
      creador_email,
      creador_id: usuario.usuario_id,
      infraestructuraRecurso_id:
        infraestructuraRecurso.infraestructuraRecurso_id,
    });
    res.status(201).json({ message: "Predio created successfully", newPredio });
  } catch (error) {
    console.error("Error creating predio:", error);
    res.status(500).json({ message: "Error creating predio" });
  }
};

// Edit a specific predio by predio_id
export const editPredio = async (req, res) => {
  const { predio_id } = req.params;
  var {
    tipo_infraestructura,
    nombre_identificacion,
    comuna,
    direccion_inmueble,
    total_hectareas_predio,
    vigencia,
    anio_proceso,
    creador_email,
    creador_id,
    userToken,
    version_lock,
  } = req.body;

  // List of required attributes for predio
  const requiredAttributes = [
    "tipo_infraestructura",
    "nombre_identificacion",
    "comuna",
    "direccion_inmueble",
    "total_hectareas_predio",
    "vigencia",
    "anio_proceso",
    "creador_email",
    "creador_id",
    "userToken",
    "version_lock",
  ];

  try {
    // Check the database connection
    await checkDatabase();
    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredAttributes
    );
    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }

    var areNumbersValid = await validateNumbers([
      total_hectareas_predio,
      vigencia,
    ]);
    if (areNumbersValid == false) {
      return res.status(400).json({ message: "Invalid number format" });
    }
    if (vigencia != 0 && vigencia != 1) {
      return res.status(400).json({ message: "Invalid vigencia value" });
    }

    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: creador_email },
      },
    });

    if (!usuario) {
      return res.status(404).json({
        message:
          "User not found or does not have access to the specified resource",
      });
    }
    // Validate the user's roles
    const validRoles = [
      "Encargado",
      "Usuario General",
      "Administrador Sistema",
    ];

    const hasAccess = await checkPermisosUsuario(
      creador_email,
      validRoles,
      "OTI"
    );
    if (!hasAccess) {
      return res.status(401).json({
        message:
          "Unauthorized access: User does not have the required permissions",
      });
    }

    // Validate predio_id is a valid UUID
    if (!uuidValidate(predio_id)) {
      return res.status(400).json({ message: "Invalid predio_id" });
    }

    // Check if predio exists
    const predio = await Predio.findByPk(predio_id);
    if (!predio) {
      return res.status(404).json({ message: "Predio not found" });
    }

    const infraestructuraRecurso = await InfraestructuraRecurso.findByPk(
      predio.infraestructuraRecurso_id
    );
    if (!infraestructuraRecurso) {
      return res
        .status(404)
        .json({ message: "InfraestructuraRecurso not found" });
    }
    // If the infarestructuraRecurso is already finalized, then the predio can't be updated
    if (infraestructuraRecurso.is_finalized == true) {
      return res.status(403).json({
        message: "Can't update Predio if is already validated",
      });
    }

    tipo_infraestructura = 5;

    if (predio.version_lock != version_lock) {
      return res.status(409).json({
        message: "El predio fue modificado por otro usuario",
      });
    }
    // Update the predio record
    await predio.update({
      tipo_infraestructura,
      nombre_identificacion,
      comuna,
      direccion_inmueble,
      total_hectareas_predio,
      vigencia,
      anio_proceso,
      version_lock: version_lock + 1,
    });
    res.status(200).json({ message: "Predio updated successfully", predio });
  } catch (error) {
    console.error("Error updating predio:", error);
    res.status(500).json({ message: "Error updating predio" });
  }
};

// Delete a specific predio by predio_id
export const deletePredio = async (req, res) => {
  const { predio_id } = req.params;
  const { userEmail, userToken } = req.body;

  try {
    // Check the database connection
    await checkDatabase();
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    // Validate that creador_email exists in the Usuario model (case-insensitive)
    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: userEmail },
      },
    });
    if (!usuario) {
      return res.status(404).json({
        message:
          "User not found or does not have access to the specified resource",
      });
    }

    // Validate the user's roles
    const validRoles = [
      "Encargado",
      "Usuario General",
      "Administrador Sistema",
    ];

    const hasAccess = await checkPermisosUsuario(
      userEmail,
      validRoles,
      "OTI"
    );
    if (!hasAccess) {
      return res.status(401).json({
        message:
          "Unauthorized access: User does not have the required permissions",
      });
    }

    if (!uuidValidate(predio_id)) {
      return res.status(400).json({ message: "Invalid predio_id" });
    }

    const predio = await Predio.findByPk(predio_id);
    if (!predio) {
      return res.status(404).json({ message: "Predio not found" });
    }

    const infraestructuraRecurso = await InfraestructuraRecurso.findOne({
      where: { anio_proceso: predio.anio_proceso },
    });
    // If the infarestructuraRecurso is already finalized, then the predio can't be deleted
    if (infraestructuraRecurso.is_finalized == true) {
      return res.status(403).json({
        message: "Can't delete Predio if is already validated",
      });
    }
    await predio.destroy();
    res.status(200).json({ message: "Predio deleted successfully" });
  } catch (error) {
    console.error("Error deleting predio:", error);
    res.status(500).json({ message: "Error deleting predio" });
  }
};
