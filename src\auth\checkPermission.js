// src/auth/checkPermissions.js
export const checkPermissions = (requiredRole, requiredResource, requiredAction) => {
    return (req, res, next) => {
        const user = req.user;

        if (!user) {
            return res.status(403).send('User not authenticated');
        }

        const hasPermission = user.accesos.some(acceso =>
            acceso.rol === requiredRole &&
            acceso.recurso === requiredResource &&
            acceso.accion === requiredAction
        );

        if (!hasPermission) {
            return res.status(403).send('Forbidden: You do not have the necessary permissions');
        }

        next();
    };
};