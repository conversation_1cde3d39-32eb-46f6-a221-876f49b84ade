/** 
     * @param {*} attributes - List of variables
     * @param {*} keys  - List with the names of the variables needed
     * @returns A list with the variables named in the Keys list
     */
export const saveSelectedAttributes = (attributes, keys) => {
    let selectedAttributes = {};
    for (let key of keys) {
        if (key in attributes) {
            selectedAttributes[key] = attributes[key];
        }
    }
    return selectedAttributes;
}