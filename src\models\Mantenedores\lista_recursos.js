// src/models/Mantenedores/lista_recursos.js
import { Model, DataTypes } from "sequelize";
import sequelize from "../../database/database.js";

class Lista_Recursos extends Model {}

Lista_Recursos.init(
  {
    recurso_id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    nombre_recurso: {
      type: DataTypes.STRING,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "Lista_Recursos",
    freezeTableName: true,
  }
);

export default Lista_Recursos;