import Notificacion from "../../models/Usuario/notificacion.js";
import Usuario from "../../models/Usuario/usuario.js";
import { checkAtributosNecesarios } from "../../utils/checkAtributosNecesarios.js";
import { sendNotificationToUser } from "../../app.js";
import { Op } from "sequelize";

/**
 * <PERSON><PERSON> to fetch a "Notificacion" given the user email.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const getNotificacion = async (req, res) => {
  try {
    // Extract email from request
    const email = req.params.usuario_email;
    console.log(email);

    // Check if the email is provided
    if (!email) {
      return res.status(400).json({ message: "Email is required." });
    }

    // Query the database for the user by email
    const user = await Usuario.findOne({
      where: {
        email: {
          [Op.iLike]: email, // Case-insensitive search
        },
      },
    });

    // If user not found, return an error
    if (!user) {
      return res.status(404).json({ message: "User not found." });
    }

    const notificaciones = await Notificacion.findAll({
      where: { dest_email: user.email },
    });

    // Respond with the notifications
    return res.status(200).json({ notificaciones });
  } catch (error) {
    // Handle unexpected errors
    console.error(error);
    return res.status(500).json({ message: "Server error." });
  }
};

/**
 * Handler to fetch to count the "Notificacion" addressed to the user
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const countNotificacion = async (req, res) => {
  try {
    // Extract email from request
    const email = req.params.usuario_email;
    console.log(email);

    // Check if the email is provided
    if (!email) {
      return res.status(400).json({ message: "Email is required." });
    }

    // Query the database for the user by email
    const user = await Usuario.findOne({
      where: {
        email: {
          [Op.iLike]: email, // Case-insensitive search
        },
      },
    });

    // If user not found, return an error
    if (!user) {
      return res.status(404).json({ message: "User not found." });
    }

    const notificaciones = await Notificacion.findAll({
      where: { dest_email: { [Op.iLike]: user.email } },  // Case-insensitive search
    });

    // Count the number of notifications
    const notificationCount = notificaciones.length;
    console.log(notificationCount);
    // Respond with the count of notifications
    return res.status(200).json({ count: notificationCount });
  } catch (error) {
    // Handle unexpected errors
    console.error(error);
    return res.status(500).json({ message: "Server error." });
  }
};

/**
 * Handler to fetch all "Notificacion" from the database and return them in a sorted order.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const getAllNotificacion = async (req, res) => {};

/**
 * Handler to create a "Bea" in the database.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const postNotificacion = async (req, res) => {
  const {
    titulo,
    mensaje,
    tipo,
    estado,
    ocultar,
    eliminar,
    destinatario,
    creador,
  } = req.body;
  const requiredAttributes = [
    "titulo",
    "mensaje",
    "tipo",
    "estado",
    "ocultar",
    "eliminar",
    "destinatario",
    "creador",
  ];

  try {
    // Check if the body has all the necessary attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredAttributes
    );
    if (missingAttributes.length > 0) {
      return res
        .status(400)
        .json({ message: "Faltan atributos", missingAttributes });
    }

    const creador_Data = await Usuario.findOne({
      where: { email: { [Op.iLike]: creador } },
    });
    const destinatario_Data = await Usuario.findOne({
      where: { email: { [Op.iLike]: destinatario } },
    });

    if (!creador_Data || !destinatario_Data) {
      return res
        .status(400)
        .json({ message: "Faltan usuario no encontrado", missingAttributes });
    }

    // Create the Bea
    const createNotificacion = await Notificacion.create({
      titulo: titulo,
      mensaje: mensaje,
      tipo: tipo,
      estado: estado,
      ocultar: ocultar,
      eliminar: eliminar,
      dest_email: destinatario_Data.email,
      destinatario: destinatario_Data.usuario_id,
      creador_email: creador_Data.email,
      usuario_id: creador_Data.usuario_id,
    });
    await sendNotificationToUser(destinatario_Data.usuario_id, "EXAMPLE");
    res.status(201).json({
      ok: true,
      status: 201,
      message: "Notificacion creada en la base de datos",
    });
  } catch (error) {
    // Handle other types of errors
    console.error(error);
    res.status(500).send("Error handling data");
  }
};
