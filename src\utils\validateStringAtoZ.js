/**
 * Validates an array of strings to ensure all contain only letters from a to z (case-insensitive).
 * @param {string[]} strings - Array of strings to validate.
 * @returns {boolean} - Returns true if all strings are valid, otherwise false.
 */
const validateStringsAZ = (strings) => {
  const regex = /^[a-z]+$/i; // Regular expression to match letters a-z (case-insensitive)

  // Loop through each string in the array
  for (let str of strings) {
    // Check if the value is not a string or contains invalid characters
    if (typeof str !== "string" || !regex.test(str)) {
      return false; // Invalid if not a string or doesn't match the pattern
    }
  }

  // If all strings pass the checks, return true
  return true;
};

export default validateStringsAZ;
