// src/models/Mantenedores/lista_roles.js
import { Model, DataTypes } from "sequelize";
import sequelize from "../../database/database.js";

class Lista_Roles extends Model {}

Lista_Roles.init(
  {
    rol_id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    nombre_rol: {
      type: DataTypes.STRING,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "Lista_Roles",
    freezeTableName: true,
  }
);

export default Lista_Roles;
