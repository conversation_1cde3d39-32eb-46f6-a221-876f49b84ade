import Acceso from "../models/Usuario/acceso.js";
import Usuario from "../models/Usuario/usuario.js";
import { sequelize } from "../database/database.js";
import { Op } from "sequelize";

const checkDatabase = async () => {
  try {
    console.log("Checking database connection...");
    await sequelize.authenticate();
    console.log("Database is connected successfully.");
  } catch (error) {
    console.error("Database connection error:", error);
    return res.status(400).json({ message: "Database not connected" });
  }
};
/**
 *
 * @param {* String - The email of the user} userEmail
 * @param {* Boolean - If "administrador de sistema" required} isAdminRequired
 * @param {* String - Lista de permisos} validRoles
 * @param {* String - recurso o "area"} recurso
 * @returns
 */
export async function checkPermisosUsuario(userEmail, validRoles, recurso) {
  try {
    await checkDatabase();
    // Validate that creador_email exists in the Usuario model (case-insensitive)
    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: userEmail },
      },
      include: {
        model: Acceso,
        as: "accesos",
        where: { recurso: [recurso, "Administracion PIU"] },
      },
    });
    if (!usuario) {
      return false;
    }

    const hasValidRole = usuario.accesos.some((acceso) =>
      validRoles.includes(acceso.rol)
    );
    console.log("rol valido: ", hasValidRole);
    if (!hasValidRole) {
      return false;
    }
    return true;
  } catch (error) {
    console.error(error);
    return false;
  }
}
