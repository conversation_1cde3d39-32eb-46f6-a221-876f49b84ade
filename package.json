{"name": "backend-piu", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"dev": "nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.8.4", "body-parser": "^1.20.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.0", "iconv-lite": "^0.6.3", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.1.0", "morgan": "^1.10.0", "nodemailer": "^6.9.15", "papaparse": "^5.4.1", "pg": "^8.11.5", "pg-hstore": "^2.3.4", "sequelize": "^6.37.3", "uuid-validate": "^0.0.3", "ws": "^8.18.0"}, "devDependencies": {"nodemon": "^3.1.1", "sequelize-cli": "^6.6.2", "sequelize-erd": "^1.3.1"}}