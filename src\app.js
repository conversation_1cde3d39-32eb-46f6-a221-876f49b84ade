// src/app.js
import express from "express";
import morgan from "morgan";
import cors from "cors";
import bodyParser from "body-parser";
import router from "./router/router.js";
import { createServer } from "http";
import { WebSocketServer, WebSocket } from "ws";
import Notificacion from "./models/Usuario/notificacion.js";
import Usuario from "./models/Usuario/usuario.js";
import { Op } from "sequelize";

const app = express();
const server = createServer(app);
const wss = new WebSocketServer({ server });
const connectedClients = {}; // Store connected clients by user ID

// WebSocket connection event
wss.on("connection", (ws) => {
  console.log("Client connected");

  ws.on("message", async (message) => {
    try {
      const parsedMessage = JSON.parse(message);
      if (!parsedMessage.email) {
        ws.send(JSON.stringify({ error: "No email provided" }));
        return;
      }

      const userEmail = parsedMessage.email;
      console.log("userEmail: " + userEmail);

      const usuario = await Usuario.findOne({
        where: { email: { [Op.iLike]: userEmail } },
      });

      if (usuario) {
        connectedClients[usuario.usuario_id] = { ws, email: userEmail }; // Store both ws and email
        console.log(`User reconnected: ${usuario.usuario_id}`);
        ws.send(
          JSON.stringify({
            message: "Connection restored",
            userId: usuario.usuario_id,
          })
        );
      } else {
        ws.send(JSON.stringify({ error: "User not found" }));
      }
    } catch (error) {
      ws.send(JSON.stringify({ error: "Failed to process message" }));
    }
  });

  ws.on("close", () => {
    for (const [userId, clientData] of Object.entries(connectedClients)) {
      if (clientData.ws === ws) {
        console.log(`Removing user: ${userId} from connected clients`);
        delete connectedClients[userId];
        break;
      }
    }
  });
});

// Function to send notifications to a specific user
const sendNotificationToUser = async (userId, notification) => {
  const user = await Usuario.findByPk(userId);
  if (!user) {
    console.log("User not found for ID:", userId);
    return;
  }
  console.log("usuario");
  console.log(user);

  const clientData = connectedClients[userId.toString()];
  if (
    clientData &&
    clientData.email.toLowerCase() === user.email.toLowerCase() &&
    clientData.ws.readyState === WebSocket.OPEN
  ) {
    clientData.ws.send(
      JSON.stringify({ message: "NEW_NOTIFICATION", type: "info" })
    );
    console.log("Notification sent to:", user.email);
  } else {
    console.log(`No active WebSocket connection for user ${user.email}`);
  }
};

app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));
app.use(cors());
app.use(morgan("dev"));
app.use(bodyParser.json());

app.use("/api/v1", router);

export { app, server, sendNotificationToUser };
