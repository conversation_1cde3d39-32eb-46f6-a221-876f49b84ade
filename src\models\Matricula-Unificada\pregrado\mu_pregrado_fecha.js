import { Model, DataTypes } from "sequelize";
import sequelize from "../../../database/database.js";

class MU_pregrado_fecha extends Model {}

MU_pregrado_fecha.init(
  {
    mu_pregrado_fecha_id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    anio_proceso: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: "Anio del proceso a reportar.",
    },
    etapa_proceso: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: "Etapa en la que es cargado el estudiante.",
    },    
    validated_by: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    fecha_inicio: { 
        type: DataTypes.DATEONLY, 
        allowNull: false, 
        comment: "Fecha inicio de la etapa actual.",
    },
    fecha_termino: { 
        type: DataTypes.DATEONLY, 
        allowNull: false, 
        comment: "Fecha termino de la etapa actual.",
    },

    creador_email: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isEmail: true, // Ensures the email format is valid
      },
      comment:
        "Email del usuario que crea el registro. Usar formato de correo electronico.",
    },
  },
  {
    sequelize,
    modelName: "MU_pregrado_fecha",
    freezeTableName: true,
    indexes: [
      {
        unique: true,
        fields: ["anio_proceso", "etapa_proceso"], // Composite unique constraint
      },
    ],
  }
);

export default MU_pregrado_fecha;
