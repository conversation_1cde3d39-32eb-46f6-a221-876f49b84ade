import { checkAtributosNecesarios } from "../../utils/checkAtributosNecesarios.js";
import uuidValidate from "uuid-validate";
import Lista_Area_Institucional from "../../models/Mantenedores/lista_areas_institucionales.js";

/**
 * Fetch all area institucional associated with a specific infraestructuraRecurso_id.
 */
export const getAllAreaInsitucional = async (req, res) => {
  try {
    const areaInstitucional = await Lista_Area_Institucional.findAll();

    if (areaInstitucional.length === 0) {
      return res.status(404).json({ message: "No Area Institucional found" });
    }

    res.status(200).json(areaInstitucional);
  } catch (error) {
    console.error("Error fetching all Area Institucional:", error);
    res.status(500).json({ message: "Error fetching Area Institucional" });
  }
};

/**
 * Create a new Area Institucional
 */
export const postAreaInstitucional = async (req, res) => {
  const { nombre_area_institucional } = req.body;

  // List of required attributes for biblioteca
  const requiredAreaInstitucionalFields = ["nombre_area_institucional"];

  try {
    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredAreaInstitucionalFields
    );
    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }

    // Create the new biblioteca record
    const newAreaInstitucional = await Lista_Area_Institucional.create({
      nombre_area_institucional,
    });
    res.status(201).json({
      message: "Area Institucional created successfully",
      newAreaInstitucional,
    });
  } catch (error) {
    console.error("Error creating Area Institucional:", error);
    res.status(500).json({ message: "Error creating Area Institucional" });
  }
};

/**
 * Delete a specific Area Institucional given it id.
 */
export const deleteAreaInstitucional = async (req, res) => {
  const { area_institucional_id } = req.params;

  try {
    if (!uuidValidate(area_institucional_id)) {
      return res.status(400).json({ message: "Invalid area_institucional_id" });
    }

    const areaInstitucional = await Lista_Area_Institucional.findByPk(
      area_institucional_id
    );
    if (!areaInstitucional) {
      return res.status(404).json({ message: "Area institucional not found" });
    }

    await areaInstitucional.destroy();
    res
      .status(200)
      .json({ message: "Area institucional deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: "Error deleting Area institucional" });
  }
};
