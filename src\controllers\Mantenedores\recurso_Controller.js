import { checkAtributosNecesarios } from "../../utils/checkAtributosNecesarios.js";
import uuidValidate from "uuid-validate";
import Lista_Recursos from "../../models/Mantenedores/lista_recursos.js";
import Acceso from "../../models/Usuario/acceso.js";
import Usuario from "../../models/Usuario/usuario.js";
import { Op } from "sequelize";

/**
 * Fetch all recursos associated with a specific recursos_id.
 */
export const getAllRecursos = async (req, res) => {
  try {
    const { email } = req.params;
    // Define required attributes
    const requiredAttributes = ["email"];

    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.params,
      requiredAttributes
    );
    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }

    // Validate that "email" exists in the Usuario model (case-insensitive)
    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: email },
      },
      include: {
        model: Acceso,
        as: "accesos",
      },
    });

    // Flatten the "recurso" attributes from "usuario.accesos"
    const flattenedRecursoData = usuario.accesos.map(
      (acceso) => acceso.recurso
    );
    var recursos = [];
    // Check if "flattenedRecursoData" contains "Administracion PIU"
    if (flattenedRecursoData.includes("Administracion PIU")) {
      recursos = await Lista_Recursos.findAll();
    } else {
      recursos = await Lista_Recursos.findAll({
        where: {
          nombre_recurso: {
            [Op.in]: flattenedRecursoData, // Match any value in the flattened array
          },
        },
      });
    }

    if (recursos.length === 0) {
      return res.status(404).json({ message: "No recurso found" });
    }

    res.status(200).json(recursos);
  } catch (error) {
    console.error("Error fetching all recursos:", error);
    res.status(500).json({ message: "Error fetching recursos" });
  }
};

/**
 * Create a new recurso given a nombre_recurso
 */
export const postRecurso = async (req, res) => {
  const { nombre_recurso } = req.body;

  // List of required attributes for Recurso
  const requiredrecursosFields = ["nombre_recurso"];

  try {
    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredrecursosFields
    );
    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }

    // Create the new recurso record
    const newrecurso = await Lista_Recursos.create({
      nombre_recurso,
    });
    res.status(201).json({
      message: "recursos created successfully",
      newrecurso,
    });
  } catch (error) {
    console.error("Error creating Recurso:", error);
    res.status(500).json({ message: "Error creating Recurso" });
  }
};

/**
 * Delete a specific recursos given it id.
 */
export const deleteRecursos = async (req, res) => {
  const { recursos_id } = req.params;

  try {
    if (!uuidValidate(recursos_id)) {
      return res.status(400).json({ message: "Invalid recursos_id" });
    }

    const recursos = await Lista_Recursos.findByPk(recursos_id);
    if (!recursos) {
      return res.status(404).json({ message: "Recursos not found" });
    }

    await recursos.destroy();
    res.status(200).json({ message: "Recursos deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: "Error deleting Recursos" });
  }
};
