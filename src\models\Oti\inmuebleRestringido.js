import { Model, DataTypes } from "sequelize";
import sequelize from "../../database/database.js";

class InmuebleRestringido extends Model {}

InmuebleRestringido.init(
  {
    inmuebleRestringido_id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    tipo_infraestructura: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    nombre_identificacion: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    comuna: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    direccion_inmueble: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    ur_desc_actividades: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    ur_total_m2_terreno: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    ur_total_m2_construidos: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    ur_situacion_tenencia: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    ur_desc_tenencia_otra: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    vigencia: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    // Sequelize optimistic lock to prevent data override
    version_lock: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
    },
    anio_proceso: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    creador_email: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    creador_id: {
      type: DataTypes.UUID,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "InmuebleRestringido",
    freezeTableName: true,
  }
);

export default InmuebleRestringido;
