import { Model, DataTypes } from "sequelize";
import sequelize from "../../database/database.js";
import OASubProceso from "./OASubProceso.js";

class OAEtapa extends Model { }
// OAEtapa.schema('oferta_academica');

OAEtapa.init(
    {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
    },
    subproceso_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: OASubProceso, 
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
    },
    tipo: {
        type: DataTypes.ENUM('insumo', 'carga'),
        allowNull: false,
    },
    categoria: {
        type: DataTypes.ENUM('pregrado', 'postgrado', 'postitulo'),
        allowNull: true,
    },
    etapa: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    fecha_inicial: {
        type: DataTypes.DATE,
        allowNull: false, 
    },
    fecha_final: {
        type: DataTypes.DATE,
        allowNull: false,
    },
            fecha_carga: {
        type: DataTypes.DATE,
        allowNull: true
    },
    validado: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
    },
    subido_por: {
        type: DataTypes.STRING,
        allowNull: true,
        validate: { 
          isEmail: true, // Ensures the email format is valid
        },
      },

    // Add other attributes here
}, {
    sequelize,
    modelName: "OAEtapa",
    schema: 'oferta_academica',
    tableName: 'oa_etapas', // Name of the table in the database
    timestamps: false, // Disable createdAt and updatedAt if not needed
});
OASubProceso.hasMany(OAEtapa, { foreignKey: 'subproceso_id', onDelete: 'RESTRICT' });
OAEtapa.belongsTo(OASubProceso, { foreignKey: 'subproceso_id', onDelete: 'RESTRICT' });


export default OAEtapa;    