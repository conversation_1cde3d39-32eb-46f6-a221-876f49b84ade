import { checkAtributosNecesarios } from "../../utils/checkAtributosNecesarios.js";
import uuidValidate from "uuid-validate";
import InmueblePermanente from "../../models/Oti/inmueblePermanente.js";
import Usuario from "../../models/Usuario/usuario.js";
import Acceso from "../../models/Usuario/acceso.js";
import { Op } from "sequelize";
import InfraestructuraRecurso from "../../models/Oti/InfraestructuraRecurso.js";
import { checkToken, checkTokenLocal } from "../../auth/auth.js";
import validateNumbers from "../../utils/validateNumberEqualGreatZero.js";
import { sequelize } from "../../database/database.js";
import { checkPermisosUsuario } from "../../utils/checkPermisosUsuario.js";

const checkDatabase = async () => {
  try {
    console.log("Checking database connection...");
    await sequelize.authenticate();
    console.log("Database is connected successfully.");
  } catch (error) {
    console.error("Database connection error:", error);
    return res.status(400).json({ message: "Database not connected" });
  }
};

/**
 * Handler to fetch an "inmueblePermanente" by inmueblePermanente_id.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const getInmueblePermanente = async (req, res) => {
  const userToken = req.headers.authorization;
  const { inmueblePermanente_id } = req.params;

  try {
    await checkDatabase();
    // Validate creador_id as a valid UUID
    if (!uuidValidate(inmueblePermanente_id)) {
      return res
        .status(400)
        .json({ message: "Invalid inmueblePermanente_id format" });
    }
    // check if the token is valid and not expired
    if (!(await checkTokenLocal(userToken))) {
      return res.status(950).json({
        message: `Token is not valid`,
      });
    }
    // Fetch inmueblePermanente by anio_proceso
    const inmueblePermanente = await InmueblePermanente.findOne({
      where: { inmueblePermanente_id: inmueblePermanente_id },
    });

    if (!inmueblePermanente) {
      return res.status(404).json({ message: "inmueblePermanente not found" });
    }
    // Convert Sequelize instance to plain object
    const inmueblePermanentePlain = inmueblePermanente.get({ plain: true });

    // Define the attributes you want to extract
    const attributesToExtract = [
      "funcion_docencia",
      "funcion_investigacion",
      "funcion_extension",
      "funcion_adm_oficinas",
      "funcion_otras",
    ];

    const extractedValues = attributesToExtract.filter(
      (attribute) => inmueblePermanente[attribute] === "X"
    );

    inmueblePermanentePlain.funcion = extractedValues;
    // Return the found inmueblePermanente
    res.status(200).json(inmueblePermanente);
  } catch (error) {
    console.error("Error fetching inmueblePermanente:", error);
    res.status(500).json({ message: "Error fetching inmueblePermanente" });
  }
};

/**
 * Handler to fetch all "inmueblePermanente" by infraestructuraRecurso_id if it exists and is valid.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const getAllInmueblePermanente = async (req, res) => {
  const userToken = req.headers.authorization;
  const { infraestructuraRecurso_id } = req.params;

  try {
    // Check the database connection
    await checkDatabase();
    // Validate infraestructuraRecurso_id is a valid UUID
    if (!uuidValidate(infraestructuraRecurso_id)) {
      return res
        .status(400)
        .json({ message: "Invalid infraestructuraRecurso_id" });
    }
    // check if the token is valid and not expired
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }

    // Check if the infraestructuraRecurso_id exists in the InfraestructuraRecurso model
    const infraestructuraRecurso = await InfraestructuraRecurso.findByPk(
      infraestructuraRecurso_id
    );
    if (!infraestructuraRecurso) {
      return res
        .status(404)
        .json({ message: "InfraestructuraRecurso not found" });
    }

    // Fetch all inmueblePermanente records associated with infraestructuraRecurso_id, sorted by anio_proceso in descending order
    const allInmueblePermanente = await InmueblePermanente.findAll({
      where: { infraestructuraRecurso_id },
    });

    if (allInmueblePermanente.length === 0) {
      return res.status(404).json({ message: "No inmueblePermanente found" });
    }

    // Return all found inmueblePermanente records
    res.status(200).json(allInmueblePermanente);
  } catch (error) {
    console.error("Error fetching inmueblePermanente:", error);
    res.status(500).json({ message: "Error fetching inmueblePermanente" });
  }
};

/**
 * Handler to create an "inmueblePermanente" in the database.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const postInmueblePermanente = async (req, res) => {
  var {
    tipo_infraestructura,
    nombre_identificacion,
    comuna,
    direccion_inmueble,
    situacion_tenencia,
    anio_inicio_uso_inmueble,
    uso_exclusivo,
    porcentaje_uso,
    nombre_institucion_comparte,
    fecha_inicio_tenencia,
    fecha_termino,
    descripcion_otra_tenencia,
    funcion,
    desc_otras_funciones,
    total_m2_terreno,
    total_m2_edificados,
    total_salas_clases,
    capacidad_salas_clases,
    total_m2_salas_clases,
    total_auditorios,
    capacidad_auditorios,
    total_m2_auditorios,
    total_laboratorios,
    total_m2_laboratorios,
    total_talleres,
    total_m2_talleres,
    total_pc_nb_disponible,
    total_m2_casinos_cafeterias,
    total_m2_areas_verdes,
    vigencia,
    anio_proceso,
    creador_email,
    creador_id,
    userToken,
  } = req.body;
  // List of required attributes
  const requiredAttributes = [
    "tipo_infraestructura",
    "nombre_identificacion",
    "comuna",
    "direccion_inmueble",
    "situacion_tenencia",
    "anio_inicio_uso_inmueble",
    "uso_exclusivo",
    "funcion",
    "total_m2_terreno",
    "total_m2_edificados",
    "total_salas_clases",
    "capacidad_salas_clases",
    "total_m2_salas_clases",
    "total_auditorios",
    "capacidad_auditorios",
    "total_m2_auditorios",
    "total_laboratorios",
    "total_m2_laboratorios",
    "total_talleres",
    "total_m2_talleres",
    "total_pc_nb_disponible",
    "total_m2_casinos_cafeterias",
    "total_m2_areas_verdes",
    "vigencia",
    "anio_proceso",
    "creador_email",
    "userToken",
  ];

  try {
    // Check the database connection
    await checkDatabase();
    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredAttributes
    );
    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }
    if (vigencia != 0 && vigencia != 1) {
      return res.status(400).json({ message: "Invalid vigencia value" });
    }

    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }

    // Cast anio_proceso to a number and check if it's an integer
    const anioProcesoNumber = Number(anio_proceso);
    if (!Number.isInteger(anioProcesoNumber)) {
      return res
        .status(400)
        .json({ message: "anio_proceso must be an integer" });
    }

    if (total_m2_salas_clases > total_m2_edificados) {
      return res.status(400).json({
        message: "total_m2_salas_clases is greater than total_m2_edificados",
      });
    }
    if (vigencia != 0 && vigencia != 1) {
      return res.status(400).json({ message: "Invalid vigencia value" });
    }

    var areNumbersValid = await validateNumbers([
      total_m2_terreno,
      total_m2_edificados,
      total_salas_clases,
      capacidad_salas_clases,
      total_m2_salas_clases,
      total_auditorios,
      capacidad_auditorios,
      total_m2_auditorios,
      total_laboratorios,
      total_m2_laboratorios,
      total_talleres,
      total_m2_talleres,
      total_pc_nb_disponible,
      total_m2_casinos_cafeterias,
      total_m2_areas_verdes,
      vigencia,
    ]);
    if (areNumbersValid == false) {
      return res.status(400).json({ message: "Invalid number format" });
    }
    if (vigencia != 0 && vigencia != 1) {
      return res.status(400).json({ message: "Invalid vigencia value" });
    }

    // Ensure creador_email exists in the Usuario model (case-insensitive)
    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: creador_email },
      },
    });

    if (!usuario) {
      return res.status(404).json({
        message:
          "User not found or does not have access to the specified resource",
      });
    }

    // Validate the user's roles
    const validRoles = [
      "Encargado",
      "Usuario General",
      "Administrador Sistema",
    ];

    const hasAccess = await checkPermisosUsuario(
      creador_email,
      validRoles,
      "OTI"
    );
    if (!hasAccess) {
      return res.status(401).json({
        message:
          "Unauthorized access: User does not have the required permissions",
      });
    }

    const infraestructuraRecurso = await InfraestructuraRecurso.findOne({
      where: {
        anio_proceso: anioProcesoNumber,
      },
    });

    if (!infraestructuraRecurso) {
      return res.status(404).json({
        message: "infraestructuraRecurso was not found",
      });
    }
    // If the infarestructuraRecurso is already finalized, then the inmueblePermanente can't be created
    if (infraestructuraRecurso.is_finalized == true) {
      return res.status(403).json({
        message:
          "Can't create new inmuebles permanentes in a validated Infraestructura",
      });
    }
    var anio_inicio = null;
    var fecha_inicio = null;
    var fecha_termino_tenencia = null;

    anio_inicio = new Date(anio_inicio_uso_inmueble);
    anio_inicio = anio_inicio.getFullYear();

    // Checks "anio_inicio_uso_inmueble" is greater than 1800 and less than anio_proceso-1
    if (1800 > anio_inicio || anio_inicio > anioProcesoNumber - 1) {
      return res.status(400).json({ message: "anio_inicio invalid" });
    }

    // Check conditional fields based on specific conditions
    if (uso_exclusivo == 2) {
      console.log("uso_exclusivo igual a 2");
      if (typeof porcentaje_uso !== "number" || porcentaje_uso == null) {
        return res.status(400).json({ message: "porcentaje_uso is missing" });
      }
      if (
        typeof porcentaje_uso === "number" &&
        porcentaje_uso >= 1 &&
        porcentaje_uso <= 99
      ) {
        // situacion_tenencia is valid
      } else {
        // Handle invalid input
        return res.status(400).json({
          message:
            "Invalid input: porcentaje_uso must be a number between 1 and 99.",
        });
      }
      if (
        nombre_institucion_comparte == null ||
        nombre_institucion_comparte == ""
      ) {
        return res
          .status(400)
          .json({ message: "nombre_institucion_comparte is missing" });
      }
    } else {
      porcentaje_uso = null;
      nombre_institucion_comparte = null;
    }

    if (
      typeof situacion_tenencia === "number" &&
      situacion_tenencia >= 1 &&
      situacion_tenencia <= 6
    ) {
      // situacion_tenencia is valid
    } else {
      // Handle invalid input
      return res.status(400).json({
        message:
          "Invalid input: situacion_tenencia must be a number between 1 and 6.",
      });
    }

    if (situacion_tenencia > 1 && situacion_tenencia < 6) {
      console.log("situacion_tenencia entre 1 y 6");
      // check if the "fecha_inicio_tenencia" and "fecha_termino" are empty strings
      if (fecha_inicio_tenencia === "" || fecha_termino === "") {
        return res
          .status(400)
          .json({ message: "fecha_inicio_tenencia is invalid" });
      }
      const date = new Date(fecha_inicio_tenencia);
      if (isNaN(date.getTime())) {
        return res.status(400).json({
          message: "fecha_inicio_tenencia is invalid",
        });
      }
      const date2 = new Date(fecha_termino);
      if (isNaN(date2.getTime())) {
        return res.status(400).json({
          message: "fecha_termino is invalid",
        });
      }
      // Comprobation to check if the date "fecha_inicio_tenencia" is greater than 1800 and less than the anio_proceso-1
      if (
        new Date(fecha_inicio_tenencia).getFullYear() < 1800 ||
        new Date(fecha_inicio_tenencia).getFullYear() > anioProcesoNumber - 1
      ) {
        return res.status(400).json({
          message: "fecha_inicio_tenencia is greater than fecha_termino",
        });
      }
      // Comprobation to check if the date "fecha_Termino" is greater than anio proceso
      if (new Date(fecha_termino).getFullYear() < anioProcesoNumber) {
        return res.status(400).json({
          message: "fecha_Termino is greater than anioProceso",
        });
      }
      // Comprobation to check if the "fecha_inicio_tenencia" and "fecha_termino" can be converted to date
      if (
        isNaN(new Date(fecha_inicio_tenencia)) ||
        isNaN(new Date(fecha_termino))
      ) {
        return res.status(400).json({ message: "Invalid date format" });
      }
      if (new Date(fecha_termino) <= new Date(fecha_inicio_tenencia)) {
        return res.status(400).json({ message: "fecha_termino is invalid" });
      }
      fecha_inicio = new Date(fecha_inicio_tenencia);
      fecha_inicio = fecha_inicio.toISOString().slice(0, 7);
      fecha_termino_tenencia = new Date(fecha_termino);
      fecha_termino_tenencia = fecha_termino_tenencia.toISOString().slice(0, 7);
      descripcion_otra_tenencia = null;
    } else if (situacion_tenencia == 1) {
      fecha_inicio_tenencia = null;
      fecha_termino = null;
      descripcion_otra_tenencia = null;
    }

    if (situacion_tenencia == 6) {
      console.log("situacion_tenencia igual a 6");
      // Check if the "fecha_inicio_tenencia" and "descripcion_otra_tenencia" are empty strings
      if (fecha_inicio_tenencia === "" || descripcion_otra_tenencia === "") {
        console.log("4");
        return res.status(400).json({ message: "A parameter is missing" });
      }
      const date = new Date(fecha_inicio_tenencia);
      if (isNaN(date.getTime())) {
        return res.status(400).json({
          message: "fecha_inicio_tenencia is invalid",
        });
      }
      fecha_inicio = new Date(fecha_inicio_tenencia);
      fecha_inicio = fecha_inicio.toISOString().slice(0, 7);
      // Set fecha_termino to null
      fecha_termino = null;
    }

    // Validate that 'funcion' is an array with values
    if (!Array.isArray(funcion) || funcion.length === 0) {
      return res
        .status(400)
        .json({ message: "'funcion' must be a non-empty array" });
    }

    if (funcion.includes("funcion_otras")) {
      console.log("funcion_otras");
      if (desc_otras_funciones === "") {
        console.log("5");
        return res.status(400).json({ message: "A parameter is missing" });
      }
    } else {
      if (desc_otras_funciones != null && desc_otras_funciones != "") {
        return res
          .status(400)
          .json({ message: "desc_otras_funciones is not required" });
      }
    }

    const funcionMapping = {
      funcion_docencia: null,
      funcion_investigacion: null,
      funcion_extension: null,
      funcion_adm_oficinas: null,
      funcion_otras: null,
    };

    // Loop through each key in the funcionMapping and set "X" if the key is in the 'funcion' array
    for (const key in funcionMapping) {
      if (funcion.includes(key)) {
        funcionMapping[key] = "X";
      }
    }

    // Check if at least one attribute has "X"
    const hasValidFunction = Object.values(funcionMapping).includes("X");

    if (!hasValidFunction) {
      // Return 400 error for invalid data
      return res.status(400).json({
        message:
          "400: Invalid input - at least one function must be marked as 'X'.",
      });
    }
    console.log(funcionMapping);
    // Create the new inmueblePermanente record
    const newInmueblePermanente = await InmueblePermanente.create({
      tipo_infraestructura: 1,
      nombre_identificacion,
      comuna,
      direccion_inmueble,
      situacion_tenencia,
      anio_inicio_uso_inmueble: anio_inicio,
      uso_exclusivo,
      porcentaje_uso,
      nombre_institucion_comparte,
      fecha_inicio_tenencia: fecha_inicio,
      fecha_termino: fecha_termino_tenencia,
      descripcion_otra_tenencia,
      funcion_docencia: funcionMapping.funcion_docencia,
      funcion_investigacion: funcionMapping.funcion_investigacion,
      funcion_extension: funcionMapping.funcion_extension,
      funcion_adm_oficinas: funcionMapping.funcion_adm_oficinas,
      funcion_otras: funcionMapping.funcion_otras,
      desc_otras_funciones,
      total_m2_terreno,
      total_m2_edificados,
      total_salas_clases,
      capacidad_salas_clases,
      total_m2_salas_clases,
      total_auditorios,
      capacidad_auditorios,
      total_m2_auditorios,
      total_laboratorios,
      total_m2_laboratorios,
      total_talleres,
      total_m2_talleres,
      total_pc_nb_disponible,
      total_m2_casinos_cafeterias,
      total_m2_areas_verdes,
      vigencia,
      anio_proceso: anioProcesoNumber,
      creador_email,
      creador_id: usuario.usuario_id,
      infraestructuraRecurso_id:
        infraestructuraRecurso.infraestructuraRecurso_id,
    });

    // Return the created record
    return res.status(201).json(newInmueblePermanente);
  } catch (error) {
    console.error("Error creating inmueblePermanente:", error);
    return res
      .status(500)
      .json({ message: "Error creating inmueblePermanente" });
  }
};

/**
 * Handler to edit an "inmueblePermanente" in the database.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const editInmueblePermanente = async (req, res) => {
  //todo check the attributes that arrives
  var {
    nombre_identificacion,
    comuna,
    direccion_inmueble,
    situacion_tenencia,
    anio_inicio_uso_inmueble,
    uso_exclusivo,
    porcentaje_uso,
    nombre_institucion_comparte,
    fecha_inicio_tenencia,
    fecha_termino,
    descripcion_otra_tenencia,
    funcion,
    desc_otras_funciones,
    total_m2_terreno,
    total_m2_edificados,
    total_salas_clases,
    capacidad_salas_clases,
    total_m2_salas_clases,
    total_auditorios,
    capacidad_auditorios,
    total_m2_auditorios,
    total_laboratorios,
    total_m2_laboratorios,
    total_talleres,
    total_m2_talleres,
    total_pc_nb_disponible,
    total_m2_casinos_cafeterias,
    total_m2_areas_verdes,
    vigencia,
    anio_proceso,
    creador_email,
    creador_id,
    userToken,
    version_lock,
  } = req.body;

  // List of required attributes
  const requiredAttributes = [
    "tipo_infraestructura",
    "nombre_identificacion",
    "comuna",
    "direccion_inmueble",
    "situacion_tenencia",
    "anio_inicio_uso_inmueble",
    "uso_exclusivo",
    "funcion",
    "total_m2_terreno",
    "total_m2_edificados",
    "total_salas_clases",
    "capacidad_salas_clases",
    "total_m2_salas_clases",
    "total_auditorios",
    "capacidad_auditorios",
    "total_m2_auditorios",
    "total_laboratorios",
    "total_m2_laboratorios",
    "total_talleres",
    "total_m2_talleres",
    "total_pc_nb_disponible",
    "total_m2_casinos_cafeterias",
    "total_m2_areas_verdes",
    "vigencia",
    "anio_proceso",
    "userToken",
    "version_lock",
  ];
  const { inmueblePermanente_id } = req.params; // Get inmueblePermanente_id from the URL parameters

  try {
    // Check the database connection
    await checkDatabase();
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    // Cast anio_proceso to a number and check if it's an integer
    const anioProcesoNumber = Number(anio_proceso);
    if (!Number.isInteger(anioProcesoNumber)) {
      return res
        .status(400)
        .json({ message: "anio_proceso must be an integer" });
    }
    if (vigencia != 0 && vigencia != 1) {
      return res.status(400).json({ message: "Invalid vigencia value" });
    }

    var areNumbersValid = await validateNumbers([
      total_m2_terreno,
      total_m2_edificados,
      total_salas_clases,
      capacidad_salas_clases,
      total_m2_salas_clases,
      total_auditorios,
      capacidad_auditorios,
      total_m2_auditorios,
      total_laboratorios,
      total_m2_laboratorios,
      total_talleres,
      total_m2_talleres,
      total_pc_nb_disponible,
      total_m2_casinos_cafeterias,
      total_m2_areas_verdes,
      vigencia,
    ]);
    if (areNumbersValid == false) {
      return res.status(400).json({ message: "Invalid number format" });
    }

    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: creador_email },
      },
    });

    if (!usuario) {
      return res.status(404).json({
        message:
          "User not found or does not have access to the specified resource",
      });
    }

    // Validate the user's roles
    const validRoles = [
      "Encargado",
      "Usuario General",
      "Administrador Sistema",
    ];

    const hasAccess = await checkPermisosUsuario(
      creador_email,
      validRoles,
      "OTI"
    );
    if (!hasAccess) {
      return res.status(401).json({
        message:
          "Unauthorized access: User does not have the required permissions",
      });
    }

    // Fetch the existing inmueblePermanente record
    const inmueblePermanente = await InmueblePermanente.findByPk(
      inmueblePermanente_id
    );

    if (!inmueblePermanente) {
      return res.status(404).json({ message: "inmueblePermanente not found" });
    }
    const infraestructuraRecurso = await InfraestructuraRecurso.findOne({
      where: { anio_proceso: inmueblePermanente.anio_proceso },
    });
    // If the infarestructuraRecurso is already finalized, then the inmueblePermanente can't be updated
    if (infraestructuraRecurso.is_finalized == true) {
      return res.status(403).json({
        message:
          "Can't update a inmueble permanente of a validated Infraestructura",
      });
    }
    if (total_m2_salas_clases > total_m2_edificados) {
      return res.status(400).json({
        message: "total_m2_salas_clases is greater than total_m2_edificados",
      });
    }

    var anio_inicio = null;
    var fecha_inicio = null;
    var fecha_termino_tenencia = null;

    anio_inicio = new Date(anio_inicio_uso_inmueble);
    anio_inicio = anio_inicio.getFullYear();

    // Check conditional fields based on specific conditions
    if (uso_exclusivo == 2) {
      console.log("uso_exclusivo igual a 2");
      if (typeof porcentaje_uso !== "number" || porcentaje_uso == null) {
        return res.status(400).json({ message: "porcentaje_uso is missing" });
      }
      if (
        typeof porcentaje_uso === "number" &&
        porcentaje_uso >= 1 &&
        porcentaje_uso <= 99
      ) {
        // situacion_tenencia is valid
      } else {
        // Handle invalid input
        return res.status(400).json({
          message:
            "Invalid input: porcentaje_uso must be a number between 1 and 99.",
        });
      }
      if (
        nombre_institucion_comparte == null ||
        nombre_institucion_comparte == ""
      ) {
        return res
          .status(400)
          .json({ message: "nombre_institucion_comparte is missing" });
      }
    } else {
      porcentaje_uso = null;
      nombre_institucion_comparte = null;
    }

    if (
      typeof situacion_tenencia === "number" &&
      situacion_tenencia >= 1 &&
      situacion_tenencia <= 6
    ) {
      // situacion_tenencia is valid
    } else {
      // Handle invalid input
      return res.status(400).json({
        message:
          "Invalid input: situacion_tenencia must be a number between 1 and 6.",
      });
    }

    if (situacion_tenencia > 1 && situacion_tenencia < 6) {
      console.log("situacion_tenencia entre 1 y 6");
      if (fecha_inicio_tenencia === "" || fecha_termino === "") {
        console.log("3");
        return res.status(400).json({ message: "A parameter is missing" });
      }

      const date = new Date(fecha_inicio_tenencia);
      if (isNaN(date.getTime())) {
        return res.status(400).json({
          message: "fecha_inicio_tenencia is invalid",
        });
      }
      const date2 = new Date(fecha_termino);
      if (isNaN(date2.getTime())) {
        return res.status(400).json({
          message: "fecha_termino is invalid",
        });
      }
      // Comprobation to check if the date "fecha_inicio_tenencia" is greater than 1800 and less than the anio_proceso-1
      if (
        new Date(fecha_inicio_tenencia).getFullYear() < 1800 ||
        new Date(fecha_inicio_tenencia).getFullYear() > anioProcesoNumber - 1
      ) {
        return res.status(400).json({
          message: "fecha_inicio_tenencia is greater than fecha_termino",
        });
      }
      // Comprobation to check if the date "fecha_Termino" is greater than anio proceso
      if (new Date(fecha_termino).getFullYear() < anioProcesoNumber) {
        return res.status(400).json({
          message: "fecha_Termino is greater than anioProceso",
        });
      }
      fecha_inicio = new Date(fecha_inicio_tenencia);
      fecha_inicio = fecha_inicio.toISOString().slice(0, 7);
      fecha_termino_tenencia = new Date(fecha_termino);
      fecha_termino_tenencia = fecha_termino_tenencia.toISOString().slice(0, 7);
      descripcion_otra_tenencia = null;
    } else if (situacion_tenencia == 1) {
      fecha_inicio_tenencia = null;
      fecha_termino = null;
      descripcion_otra_tenencia = null;
    }

    if (situacion_tenencia == 6) {
      console.log("situacion_tenencia igual a 6");
      if (fecha_inicio_tenencia === "" || descripcion_otra_tenencia === "") {
        console.log("4");
        return res.status(400).json({ message: "A parameter is missing" });
      }
      const date = new Date(fecha_inicio_tenencia);
      if (isNaN(date.getTime())) {
        return res.status(400).json({
          message: "fecha_inicio_tenencia is invalid",
        });
      }
      fecha_inicio = new Date(fecha_inicio_tenencia);
      fecha_inicio = fecha_inicio.toISOString().slice(0, 7);
      fecha_termino = null;
    }

    // Validate that 'funcion' is an array with values
    if (!Array.isArray(funcion) || funcion.length === 0) {
      return res
        .status(400)
        .json({ message: "'funcion' must be a non-empty array" });
    }

    if (funcion.includes("funcion_otras")) {
      console.log("funcion_otras");
      if (desc_otras_funciones === "") {
        console.log("5");
        return res.status(400).json({ message: "A parameter is missing" });
      }
    } else {
      if (desc_otras_funciones != null && desc_otras_funciones != "") {
        return res
          .status(400)
          .json({ message: "desc_otras_funciones is not required" });
      }
    }

    const funcionMapping = {
      funcion_docencia: null,
      funcion_investigacion: null,
      funcion_extension: null,
      funcion_adm_oficinas: null,
      funcion_otras: null,
    };

    // Loop through each key in the funcionMapping and set "X" if the key is in the 'funcion' array
    for (const key in funcionMapping) {
      if (funcion.includes(key)) {
        funcionMapping[key] = "X";
      }
    }

    if (inmueblePermanente.version_lock != version_lock) {
      return res.status(409).json({
        message: "El inmueble Permanente fue modificado por otro usuario",
      });
    }
    console.log(funcionMapping);
    // Update the inmueblePermanente with the validated new fields
    await inmueblePermanente.update({
      nombre_identificacion,
      comuna,
      direccion_inmueble,
      situacion_tenencia,
      anio_inicio_uso_inmueble: anio_inicio,
      uso_exclusivo,
      porcentaje_uso,
      nombre_institucion_comparte,
      fecha_inicio_tenencia: fecha_inicio,
      fecha_termino: fecha_termino_tenencia,
      descripcion_otra_tenencia,
      funcion_docencia: funcionMapping.funcion_docencia,
      funcion_investigacion: funcionMapping.funcion_investigacion,
      funcion_extension: funcionMapping.funcion_extension,
      funcion_adm_oficinas: funcionMapping.funcion_adm_oficinas,
      funcion_otras: funcionMapping.funcion_otras,
      desc_otras_funciones,
      total_m2_terreno,
      total_m2_edificados,
      total_salas_clases,
      capacidad_salas_clases,
      total_m2_salas_clases,
      total_auditorios,
      capacidad_auditorios,
      total_m2_auditorios,
      total_laboratorios,
      total_m2_laboratorios,
      total_talleres,
      total_m2_talleres,
      total_pc_nb_disponible,
      total_m2_casinos_cafeterias,
      total_m2_areas_verdes,
      vigencia,
      anio_proceso: anioProcesoNumber,
      version_lock: version_lock + 1,
    });

    // Return the updated inmueblePermanente
    res.status(200).json(inmueblePermanente);
  } catch (error) {
    console.error("Error updating inmueblePermanente:", error);
    res.status(500).json({ message: "Error updating inmueblePermanente" });
  }
};

/**
 * Handler to delete an "inmueblePermanente" from the database.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const deleteInmueblePermanente = async (req, res) => {
  const { inmueblePermanente_id } = req.params; // Use inmueblePermanente_id from the request params
  const { userEmail, userToken } = req.body;

  try {
    // Check the database connection
    await checkDatabase();
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    // Validate that creador_email exists in the Usuario model (case-insensitive)
    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: userEmail },
      },
    });
    if (!usuario) {
      return res.status(404).json({
        message:
          "User not found or does not have access to the specified resource",
      });
    }

    // Validate the user's roles
    const validRoles = [
      "Encargado",
      "Usuario General",
      "Administrador Sistema",
    ];

    const hasAccess = await checkPermisosUsuario(userEmail, validRoles, "OTI");
    if (!hasAccess) {
      return res.status(401).json({
        message:
          "Unauthorized access: User does not have the required permissions",
      });
    }

    // Fetch the existing inmueblePermanente record
    const inmueblePermanente = await InmueblePermanente.findByPk(
      inmueblePermanente_id
    );

    if (!inmueblePermanente) {
      return res.status(404).json({ message: "inmueblePermanente not found" });
    }

    const infraestructuraRecurso = await InfraestructuraRecurso.findOne({
      where: { anio_proceso: inmueblePermanente.anio_proceso },
    });
    // If the infarestructuraRecurso is already finalized, then the inmueblePermanente can't be deleted
    if (infraestructuraRecurso.is_finalized == true) {
      return res.status(403).json({
        message:
          "Can't delete a inmueble permanente of a validated Infraestructura",
      });
    }

    // Delete the inmueblePermanente
    await inmueblePermanente.destroy();

    // Return a success message
    res
      .status(200)
      .json({ message: "inmueblePermanente deleted successfully" });
  } catch (error) {
    console.error("Error deleting inmueblePermanente:", error);
    res.status(500).json({ message: "Error deleting inmueblePermanente" });
  }
};
