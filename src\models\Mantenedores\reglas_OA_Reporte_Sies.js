// src/models/Mantenedores/lista_roles.js
import { Model, DataTypes } from "sequelize";
import sequelize from "../../database/database.js";

class Reglas_OA_SIES extends Model {}

Reglas_OA_SIES.init(
  {
    regla_OA_SIES_id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    nombre_campo: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    etapa_actual: {
      type: DataTypes.ENUM("1", "2", "3"),
      allowNull: false,
    },
    tipo_acceso: {
      type: DataTypes.ENUM("pregrado", "posgrado", "postitulo"),
      allowNull: false,
    },
    es_demre: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    obligatorio: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    permite_cambios: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    especificaciones: {
      type: DataTypes.ENUM(
        "texto",
        "char",
        "numero",
        "decimal",
        "alfanumerico",
        "vacio"
      ),
      allowNull: false,
    },
    regex: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: "Reglas_OA_SIES",
    freezeTableName: true,
    indexes: [
      {
        unique: true,
        fields: ["nombre_campo", "etapa_actual", "tipo_acceso"],
      },
    ],
  }
);

export default Reglas_OA_SIES;
