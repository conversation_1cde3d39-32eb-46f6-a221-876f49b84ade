import { Model, DataTypes } from "sequelize";
import sequelize from "../../../database/database.js";
import MU_postgrado_postitulo_estudiante from "./mu_postgrado_postitulo_estudiante.js";
import MU_postgrado_postitulo_fecha from "./mu_postgrado_postitulo_fecha.js";

class MU_postgrado_postitulo_anio_proceso extends Model {}

MU_postgrado_postitulo_anio_proceso.init(
  {
    mu_postgrado_postitulo_anio_proceso_id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    anio_proceso: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: true,
    },
    is_finalized: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    upploaded_by: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    // Sequelize optimistic lock to prevent data override
    version_lock: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
    },
    creador_email: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isEmail: true, // Ensures the email format is valid
      },
    },
    etapa_actual: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
    },
    fecha_validacion_1: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: "Fecha en que la etapa actual fue validada.",
    },
    fecha_carga_1: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: "Fecha de creación del registro en la etapa 1.",
    },
    creador_id: {
      type: DataTypes.UUID,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "MU_postgrado_postitulo_anio_proceso",
    freezeTableName: true,
  }
);

// One-to-Many relationship with "MU_postgrado_postitulo_estudiante"
MU_postgrado_postitulo_anio_proceso.hasMany(MU_postgrado_postitulo_estudiante, {
  foreignKey: "mu_postgrado_postitulo_anio_proceso_id",
  onDelete: "RESTRICT",
});
MU_postgrado_postitulo_estudiante.belongsTo(
  MU_postgrado_postitulo_anio_proceso,
  {
    foreignKey: "mu_postgrado_postitulo_anio_proceso_id",
    onDelete: "RESTRICT",
  }
);
MU_postgrado_postitulo_fecha.belongsTo(MU_postgrado_postitulo_anio_proceso, {
  foreignKey: "mu_postgrado_postitulo_anio_proceso_id",
  onDelete: "RESTRICT", // Prevent deletion if referenced
  onUpdate: "CASCADE",
});
MU_postgrado_postitulo_anio_proceso.hasMany(MU_postgrado_postitulo_fecha, {
  foreignKey: "mu_postgrado_postitulo_anio_proceso_id",
  onDelete: "RESTRICT",
  onUpdate: "CASCADE",
});

export default MU_postgrado_postitulo_anio_proceso;
