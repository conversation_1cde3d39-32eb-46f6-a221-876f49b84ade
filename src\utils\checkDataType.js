/**
 * Check if all the attributes are of the data type provided
 * @param {Object} body - The request body.
 * @param {Array<string>} requiredAttributes - An array of required attribute names.
 * @param {string} datatype - The data type to check
 * @returns {Array<string>} - Returns an array of missing attribute names. If no attributes are missing, returns an empty array.
 */
export const checkDataType = (body, requiredAttributes,datatype) => {
    const missingAttributes = requiredAttributes.filter(attr => typeof body[attr] !== datatype);
    return missingAttributes;
};