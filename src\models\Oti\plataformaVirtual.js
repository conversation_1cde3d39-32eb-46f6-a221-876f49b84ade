import { Model, DataTypes } from "sequelize";
import sequelize from "../../database/database.js";

class PlataformaVirtual extends Model {}

PlataformaVirtual.init(
  {
    plataformaVirtual_id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    tipo_infraestructura: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    nombre_identificacion: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    comuna: {
      type: DataTypes.STRING,
      allowNull: false,
    },    
    direccion_inmueble: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    sistema_gestion_aprendizajes: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    sistema_video_conferencia: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    sistema_aplicacion_evaluacion: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    descripcion_plataforma_virtual: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    vigencia: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    // Sequelize optimistic lock to prevent data override
    version_lock: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
    },
    anio_proceso: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    creador_email: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    creador_id: {
      type: DataTypes.UUID,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "PlataformaVirtual",
    freezeTableName: true,
  }
);

export default PlataformaVirtual;
