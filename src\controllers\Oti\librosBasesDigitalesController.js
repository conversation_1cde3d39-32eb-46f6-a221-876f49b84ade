import { checkAtributosNecesarios } from "../../utils/checkAtributosNecesarios.js";
import uuidValidate from "uuid-validate";
import LibrosBasesDigitales from "../../models/Oti/librosBasesDigitales.js";
import { checkDataType } from "../../utils/checkDataType.js";
import Usuario from "../../models/Usuario/usuario.js";
import Acceso from "../../models/Usuario/acceso.js";
import { Op } from "sequelize";
import InfraestructuraRecurso from "../../models/Oti/InfraestructuraRecurso.js";
import { checkToken, checkTokenLocal } from "../../auth/auth.js";
import { sequelize } from "../../database/database.js";
import { checkPermisosUsuario } from "../../utils/checkPermisosUsuario.js";

const checkDatabase = async () => {
  try {
    console.log("Checking database connection...");
    await sequelize.authenticate();
    console.log("Database is connected successfully.");
  } catch (error) {
    console.error("Database connection error:", error);
    return res.status(400).json({ message: "Database not connected" });
  }
};

/**
 * Fetch a specific librosBasesDigitales by libro_id.
 */
export const getLibrosBasesDigitales = async (req, res) => {
  const userToken = req.headers.authorization;
  const { libro_id } = req.params;

  try {
    // Check the database connection
    await checkDatabase();
    if (!uuidValidate(libro_id)) {
      return res.status(400).json({ message: "Invalid libro_id" });
    }
    // check if the token is valid and not expired
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }

    const libro = await LibrosBasesDigitales.findByPk(libro_id);
    if (!libro) {
      return res.status(404).json({ message: "Libro not found" });
    }

    res.status(200).json(libro);
  } catch (error) {
    console.error("Error fetching libro:", error);
    res.status(500).json({ message: "Error fetching libro" });
  }
};

/**
 * Fetch all librosBasesDigitales associated with a specific infraestructuraRecurso_id.
 */
export const getAllLibrosBasesDigitales = async (req, res) => {
  const userToken = req.headers.authorization;
  const { infraestructuraRecurso_id } = req.params;

  try {
    // Check the database connection
    await checkDatabase();
    if (!uuidValidate(infraestructuraRecurso_id)) {
      return res
        .status(400)
        .json({ message: "Invalid infraestructuraRecurso_id" });
    }
    // check if the token is valid and not expired
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }

    const infraestructuraRecurso = await InfraestructuraRecurso.findByPk(
      infraestructuraRecurso_id
    );
    if (!infraestructuraRecurso) {
      return res
        .status(404)
        .json({ message: "InfraestructuraRecurso not found" });
    }

    const libros = await LibrosBasesDigitales.findAll({
      where: { infraestructuraRecurso_id },
    });

    if (libros.length === 0) {
      return res.status(404).json({ message: "No libros found" });
    }

    res.status(200).json(libros);
  } catch (error) {
    console.error("Error fetching all libros:", error);
    res.status(500).json({ message: "Error fetching libros" });
  }
};

/**
 * Create a new librosBasesDigitales.
 */
export const postLibrosBasesDigitales = async (req, res) => {
  const {
    tipo_infraestructura,
    nombre_identificacion,
    comuna,
    direccion_inmueble,
    total_titulos_libros_digitales,
    total_suscripciones_digitales,
    total_base_datos,
    vigencia,
    anio_proceso,
    creador_email,
    creador_id,
    userToken,
  } = req.body;

  // List of required attributes for librosBasesDigitales
  const requiredAttributes = [
    "tipo_infraestructura",
    "nombre_identificacion",
    "comuna",
    "direccion_inmueble",
    "total_titulos_libros_digitales",
    "total_suscripciones_digitales",
    "total_base_datos",
    "vigencia",
    "anio_proceso",
    "creador_email",
    "userToken",
  ];

  try {
    // Check the database connection
    await checkDatabase();
    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredAttributes
    );
    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }
    if (vigencia != 0 && vigencia != 1) {
      return res.status(400).json({ message: "Invalid vigencia value" });
    }
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }

    // Validate that creador_email exists in the Usuario model and matches the creador_id (case-insensitive using Op.iLike)
    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: creador_email },
      },
    });

    if (!usuario) {
      return res.status(404).json({
        message:
          "User not found or does not have access to the specified resource",
      });
    }

    // Validate the user's roles
    const validRoles = [
      "Encargado",
      "Usuario General",
      "Administrador Sistema",
    ];

    const hasAccess = await checkPermisosUsuario(
      creador_email,
      validRoles,
      "OTI"
    );
    if (!hasAccess) {
      return res.status(401).json({
        message:
          "Unauthorized access: User does not have the required permissions",
      });
    }

    // Validate that creador_email exists in the Usuario model and matches the creador_id (case-insensitive using Op.iLike)
    const infraestructuraRecurso = await InfraestructuraRecurso.findOne({
      where: { anio_proceso: anio_proceso },
    });

    if (!infraestructuraRecurso) {
      return res.status(404).json({
        message: "infraestructuraRecurso not found in Usuario model",
      });
    }
    // If the infarestructuraRecurso is already finalized, then the user can't create a new libroBaseDigital
    if (infraestructuraRecurso.is_finalized == true) {
      return res.status(403).json({
        message: "Can't create new LibroBaseDigital if is already validated",
      });
    }

    // Create the new librosBasesDigitales record
    const newLibro = await LibrosBasesDigitales.create({
      tipo_infraestructura: 4,
      nombre_identificacion,
      comuna,
      direccion_inmueble,
      total_titulos_libros_digitales,
      total_suscripciones_digitales,
      total_base_datos,
      vigencia,
      anio_proceso,
      creador_email,
      creador_id: usuario.usuario_id,
      infraestructuraRecurso_id:
        infraestructuraRecurso.infraestructuraRecurso_id,
    });
    res.status(201).json({ message: "Libro created successfully", newLibro });
  } catch (error) {
    console.error("Error creating libro:", error);
    res.status(500).json({ message: "Error creating libro" });
  }
};

/**
 * Edit a specific librosBasesDigitales by libro_id.
 */
export const editLibrosBasesDigitales = async (req, res) => {
  const { libro_id } = req.params;
  var {
    tipo_infraestructura,
    nombre_identificacion,
    comuna,
    direccion_inmueble,
    total_titulos_libros_digitales,
    total_suscripciones_digitales,
    total_base_datos,
    vigencia,
    anio_proceso,
    creador_email,
    creador_id,
    userToken,
    version_lock,
  } = req.body;

  // List of required attributes for librosBasesDigitales
  const requiredAttributes = [
    "tipo_infraestructura",
    "nombre_identificacion",
    "comuna",
    "direccion_inmueble",
    "total_titulos_libros_digitales",
    "total_suscripciones_digitales",
    "total_base_datos",
    "vigencia",
    "anio_proceso",
    "creador_email",
    "userToken",
    "version_lock",
  ];

  try {
    // Check the database connection
    await checkDatabase();
    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredAttributes
    );
    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }
    if (vigencia != 0 && vigencia != 1) {
      return res.status(400).json({ message: "Invalid vigencia value" });
    }
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    // Validate libro_id is a valid UUID
    if (!uuidValidate(libro_id)) {
      return res.status(400).json({ message: "Invalid libro_id" });
    }
    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: creador_email },
      },
    });

    if (!usuario) {
      return res.status(404).json({
        message:
          "User not found or does not have access to the specified resource",
      });
    }

    // Validate the user's roles
    const validRoles = [
      "Encargado",
      "Usuario General",
      "Administrador Sistema",
    ];

    const hasAccess = await checkPermisosUsuario(
      creador_email,
      validRoles,
      "OTI"
    );
    if (!hasAccess) {
      return res.status(401).json({
        message:
          "Unauthorized access: User does not have the required permissions",
      });
    }

    // Check if libro exists
    const libro = await LibrosBasesDigitales.findByPk(libro_id);
    if (!libro) {
      return res.status(404).json({ message: "Libro not found" });
    }

    const infraestructuraRecurso = await InfraestructuraRecurso.findByPk(
      libro.infraestructuraRecurso_id
    );
    if (!infraestructuraRecurso) {
      return res
        .status(404)
        .json({ message: "InfraestructuraRecurso not found" });
    }
    // If the infarestructuraRecurso is already finalized, then the user can't create a new libroBaseDigital
    if (infraestructuraRecurso.is_finalized == true) {
      return res.status(403).json({
        message: "Can't update LibroBaseDigital if is already validated",
      });
    }

    if (libro.version_lock != version_lock) {
      return res.status(409).json({
        message: "El LibroBasesDigitales fue modificado por otro usuario",
      });
    }

    tipo_infraestructura = 4;
    // Update the libro record
    await libro.update({
      tipo_infraestructura,
      nombre_identificacion,
      comuna,
      direccion_inmueble,
      total_titulos_libros_digitales,
      total_suscripciones_digitales,
      total_base_datos,
      vigencia,
      anio_proceso,
      version_lock: version_lock + 1,
    });
    res.status(200).json({ message: "Libro updated successfully", libro });
  } catch (error) {
    console.error("Error updating libro:", error);
    res.status(500).json({ message: "Error updating libro" });
  }
};

/**
 * Delete a specific librosBasesDigitales by libro_id.
 */
export const deleteLibrosBasesDigitales = async (req, res) => {
  const { libro_id } = req.params;
  const { userEmail, userToken } = req.body;

  try {
    // Check the database connection
    await checkDatabase();
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    // Validate that creador_email exists in the Usuario model (case-insensitive)
    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: userEmail },
      },
    });
    if (!usuario) {
      return res.status(404).json({
        message:
          "User not found or does not have access to the specified resource",
      });
    }

    // Validate the user's roles
    const validRoles = [
      "Encargado",
      "Usuario General",
      "Administrador Sistema",
    ];

    const hasAccess = await checkPermisosUsuario(userEmail, validRoles, "OTI");
    if (!hasAccess) {
      return res.status(401).json({
        message:
          "Unauthorized access: User does not have the required permissions",
      });
    }

    if (!uuidValidate(libro_id)) {
      return res.status(400).json({ message: "Invalid libro_id" });
    }

    const libro = await LibrosBasesDigitales.findByPk(libro_id);
    if (!libro) {
      return res.status(404).json({ message: "Libro not found" });
    }

    const infraestructuraRecurso = await InfraestructuraRecurso.findOne({
      where: { anio_proceso: libro.anio_proceso },
    });
    // If the infarestructuraRecurso is already finalized, then the user can't delete the libroBaseDigital
    if (infraestructuraRecurso.is_finalized == true) {
      return res.status(403).json({
        message: "Can't delete LibroBaseDigital if is already validated",
      });
    }

    await libro.destroy();
    res.status(200).json({ message: "Libro deleted successfully" });
  } catch (error) {
    console.error("Error deleting libro:", error);
    res.status(500).json({ message: "Error deleting libro" });
  }
};
