import { checkAtributosNecesarios } from "../../utils/checkAtributosNecesarios.js";
import uuidValidate from "uuid-validate";
import InmueblePermanente from "../../models/Oti/inmueblePermanente.js";
import { checkDataType } from "../../utils/checkDataType.js";
import Biblioteca from "../../models/Oti/biblioteca.js";
import Usuario from "../../models/Usuario/usuario.js";
import Acceso from "../../models/Usuario/acceso.js";
import { Op } from "sequelize";
import InfraestructuraRecurso from "../../models/Oti/InfraestructuraRecurso.js";
import { checkTokenLocal } from "../../auth/auth.js";
import validateNumbers from "../../utils/validateNumberEqualGreatZero.js";
import { sequelize } from "../../database/database.js";
import { checkPermisosUsuario } from "../../utils/checkPermisosUsuario.js";

const checkDatabase = async () => {
  try {
    console.log("Checking database connection...");
    await sequelize.authenticate();
    console.log("Database is connected successfully.");
  } catch (error) {
    console.error("Database connection error:", error);
    return res.status(400).json({ message: "Database not connected" });
  }
};

/**
 * Fetch a specific biblioteca by biblioteca_id.
 */
export const getBiblioteca = async (req, res) => {
  const { biblioteca_id } = req.params;
  const userToken = req.headers.authorization;

  try {
    // Check the database connection
    await checkDatabase();
    if (!uuidValidate(biblioteca_id)) {
      return res.status(400).json({ message: "Invalid biblioteca_id" });
    }
    // check if the token is valid and not expired
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }

    const biblioteca = await Biblioteca.findByPk(biblioteca_id);
    if (!biblioteca) {
      return res.status(404).json({ message: "Biblioteca not found" });
    }

    res.status(200).json(biblioteca);
  } catch (error) {
    console.error("Error fetching biblioteca:", error);
    res.status(500).json({ message: "Error fetching biblioteca" });
  }
};

/**
 * Fetch all bibliotecas associated with a specific infraestructuraRecurso_id.
 */
export const getAllBiblioteca = async (req, res) => {
  const userToken = req.headers.authorization;
  const { infraestructuraRecurso_id } = req.params;

  try {
    // Check the database connection
    await checkDatabase();
    if (!uuidValidate(infraestructuraRecurso_id)) {
      return res
        .status(400)
        .json({ message: "Invalid infraestructuraRecurso_id" });
    }

    // check if the token is valid and not expired
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    const infraestructuraRecurso = await InfraestructuraRecurso.findByPk(
      infraestructuraRecurso_id
    );
    if (!infraestructuraRecurso) {
      return res
        .status(404)
        .json({ message: "InfraestructuraRecurso not found" });
    }

    const bibliotecas = await Biblioteca.findAll({
      where: { infraestructuraRecurso_id },
    });

    if (bibliotecas.length === 0) {
      return res.status(404).json({ message: "No bibliotecas found" });
    }

    res.status(200).json(bibliotecas);
  } catch (error) {
    console.error("Error fetching all bibliotecas:", error);
    res.status(500).json({ message: "Error fetching bibliotecas" });
  }
};

/**
 * Create a new biblioteca.
 */
export const postBiblioteca = async (req, res) => {
  const {
    tipo_infraestructura,
    nombre_identificacion,
    comuna,
    direccion_inmueble,
    total_m2_biblioteca,
    total_m2_salas_lectura,
    total_profesionales_biblioteca,
    horas_personal_biblioteca,
    total_titulos_disponibles,
    total_volumenes_disponibles,
    total_suscripciones_revistas,
    vigencia,
    anio_proceso,
    creador_email,
    creador_id,
    userToken,
  } = req.body;

  // List of required attributes for biblioteca
  const requiredBibliotecaFields = [
    "tipo_infraestructura",
    "nombre_identificacion",
    "comuna",
    "direccion_inmueble",
    "total_m2_biblioteca",
    "total_m2_salas_lectura",
    "total_profesionales_biblioteca",
    "horas_personal_biblioteca",
    "total_titulos_disponibles",
    "total_volumenes_disponibles",
    "total_suscripciones_revistas",
    "vigencia",
    "anio_proceso",
    "creador_email",
    "creador_id",
    "userToken",
  ];

  try {
    // Check the database connection
    await checkDatabase();
    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredBibliotecaFields
    );
    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }

    var areNumbersValid = await validateNumbers([
      total_m2_biblioteca,
      total_m2_salas_lectura,
      total_profesionales_biblioteca,
      horas_personal_biblioteca,
      total_titulos_disponibles,
      total_volumenes_disponibles,
      total_suscripciones_revistas,
      vigencia,
    ]);
    if (areNumbersValid == false) {
      return res.status(400).json({ message: "Invalid number format" });
    }

    if (vigencia != 0 && vigencia != 1) {
      return res.status(400).json({ message: "Invalid vigencia value" });
    }

    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }

    // Validate that creador_email exists in the Usuario model (case-insensitive)
    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: creador_email },
      },
    });
    if (!usuario) {
      return res.status(404).json({
        message:
          "User not found or does not have access to the specified resource",
      });
    }

    // Validate the user's roles
    const validRoles = [
      "Encargado",
      "Usuario General",
      "Administrador Sistema",
    ];

    const hasAccess = await checkPermisosUsuario(
      creador_email,
      validRoles,
      "OTI"
    );
    if (!hasAccess) {
      return res.status(401).json({
        message:
          "Unauthorized access: User does not have the required permissions",
      });
    }

    if (total_m2_salas_lectura > total_m2_biblioteca) {
      return res.status(400).json({
        message: "total_m2_salas_lectura is greater than total_m2_edificados",
      });
    }

    // Check if infraestructuraRecurso_id exists in InfraestructuraRecurso
    const infraestructuraRecurso = await InfraestructuraRecurso.findOne({
      where: {
        anio_proceso: anio_proceso,
      },
    });

    if (!infraestructuraRecurso) {
      return res.status(404).json({
        message: "infraestructuraRecurso was not found",
      });
    }
    // If the infarestructuraRecurso is already finalized, then the biblioteca can't be created
    if (infraestructuraRecurso.is_finalized == true) {
      return res.status(403).json({
        message:
          "Can't add new biblioteca of an already validated Infraestructura",
      });
    }

    // Create the new biblioteca record
    const newBiblioteca = await Biblioteca.create({
      tipo_infraestructura: 3,
      nombre_identificacion,
      comuna,
      direccion_inmueble,
      total_m2_biblioteca,
      total_m2_salas_lectura,
      total_profesionales_biblioteca,
      horas_personal_biblioteca,
      total_titulos_disponibles,
      total_volumenes_disponibles,
      total_suscripciones_revistas,
      vigencia,
      anio_proceso,
      creador_email,
      creador_id: usuario.usuario_id,
      infraestructuraRecurso_id:
        infraestructuraRecurso.infraestructuraRecurso_id,
    });
    res
      .status(201)
      .json({ message: "Biblioteca created successfully", newBiblioteca });
  } catch (error) {
    console.error("Error creating biblioteca:", error);
    res.status(500).json({ message: "Error creating biblioteca" });
  }
};

/**
 * Edit a specific biblioteca by biblioteca_id.
 */
export const editBiblioteca = async (req, res) => {
  const { biblioteca_id } = req.params;
  var {
    tipo_infraestructura,
    nombre_identificacion,
    comuna,
    direccion_inmueble,
    total_m2_biblioteca,
    total_m2_salas_lectura,
    total_profesionales_biblioteca,
    horas_personal_biblioteca,
    total_titulos_disponibles,
    total_volumenes_disponibles,
    total_suscripciones_revistas,
    vigencia,
    anio_proceso,
    creador_email,
    creador_id,
    userToken,
    version_lock,
  } = req.body;

  // List of required attributes for biblioteca
  const requiredBibliotecaFields = [
    "tipo_infraestructura",
    "nombre_identificacion",
    "comuna",
    "direccion_inmueble",
    "total_m2_biblioteca",
    "total_m2_salas_lectura",
    "total_profesionales_biblioteca",
    "horas_personal_biblioteca",
    "total_titulos_disponibles",
    "total_volumenes_disponibles",
    "total_suscripciones_revistas",
    "vigencia",
    "anio_proceso",
    "creador_email",
    "creador_id",
    "userToken",
    "version_lock",
  ];

  try {
    // Check the database connection
    await checkDatabase();
    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredBibliotecaFields
    );
    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }

    var areNumbersValid = await validateNumbers([
      total_m2_biblioteca,
      total_m2_salas_lectura,
      total_profesionales_biblioteca,
      horas_personal_biblioteca,
      total_titulos_disponibles,
      total_volumenes_disponibles,
      total_suscripciones_revistas,
      vigencia,
    ]);

    if (total_titulos_disponibles > total_volumenes_disponibles) {
      return res.status(400).json({
        message:
          "total_titulos_disponibles is greater than total_volumenes_disponibles",
      });
    }
    if (areNumbersValid == false) {
      return res.status(400).json({ message: "Invalid number format" });
    }

    if (vigencia != 0 && vigencia != 1) {
      return res.status(400).json({ message: "Invalid vigencia value" });
    }

    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }

    // Validate that creador_email exists in the Usuario model (case-insensitive)
    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: creador_email },
      },
    });
    if (!usuario) {
      return res.status(404).json({
        message:
          "User not found or does not have access to the specified resource",
      });
    }

    if (total_m2_salas_lectura > total_m2_biblioteca) {
      return res.status(400).json({
        message: "total_m2_salas_lectura is greater than total_m2_edificados",
      });
    }

    // Validate the user's roles
    const validRoles = [
      "Encargado",
      "Usuario General",
      "Administrador Sistema",
    ];

    const hasAccess = await checkPermisosUsuario(
      creador_email,
      validRoles,
      "OTI"
    );
    if (!hasAccess) {
      return res.status(401).json({
        message:
          "Unauthorized access: User does not have the required permissions",
      });
    }
    // Validate biblioteca_id is a valid UUID
    if (!uuidValidate(biblioteca_id)) {
      return res.status(400).json({ message: "Invalid biblioteca_id" });
    }

    // Check if biblioteca exists
    const biblioteca = await Biblioteca.findByPk(biblioteca_id);
    if (!biblioteca) {
      return res.status(404).json({ message: "Biblioteca not found" });
    }

    // If infraestructuraRecurso_id is present in the request, validate it
    if (!uuidValidate(biblioteca.infraestructuraRecurso_id)) {
      return res
        .status(400)
        .json({ message: "Invalid infraestructuraRecurso_id" });
    }

    const infraestructuraRecurso = await InfraestructuraRecurso.findByPk(
      biblioteca.infraestructuraRecurso_id
    );
    if (!infraestructuraRecurso) {
      return res
        .status(404)
        .json({ message: "InfraestructuraRecurso not found" });
    }
    // If the infarestructuraRecurso is already finalized, then the biblioteca can't be edited
    if (infraestructuraRecurso.is_finalized == true) {
      return res.status(403).json({
        message:
          "Can't update values of biblioteca of an already validated Infraestructura",
      });
    }
    if (biblioteca.version_lock != version_lock) {
      return res
        .status(409)
        .json({ message: "La biblioteca fue modificada por otro usuario" });
    }
    tipo_infraestructura = 3;
    // Update the biblioteca record
    await biblioteca.update({
      tipo_infraestructura,
      nombre_identificacion,
      comuna,
      direccion_inmueble,
      total_m2_biblioteca,
      total_m2_salas_lectura,
      total_profesionales_biblioteca,
      horas_personal_biblioteca,
      total_titulos_disponibles,
      total_volumenes_disponibles,
      total_suscripciones_revistas,
      vigencia,
      anio_proceso,
      version_lock: version_lock + 1,
    });
    res
      .status(200)
      .json({ message: "Biblioteca updated successfully", biblioteca });
  } catch (error) {
    console.error("Error updating biblioteca:", error);
    res.status(500).json({ message: "Error updating biblioteca" });
  }
};

/**
 * Delete a specific biblioteca by biblioteca_id.
 */
export const deleteBiblioteca = async (req, res) => {
  const { biblioteca_id } = req.params;
  const { userEmail, userToken } = req.body;

  try {
    // Check the database connection
    await checkDatabase();
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    // Validate that creador_email exists in the Usuario model (case-insensitive)
    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: userEmail },
      },
    });
    if (!usuario) {
      return res.status(404).json({
        message:
          "User not found or does not have access to the specified resource",
      });
    }

    // Validate the user's roles
    const validRoles = [
      "Encargado",
      "Usuario General",
      "Administrador Sistema",
    ];

    const hasAccess = await checkPermisosUsuario(userEmail, validRoles, "OTI");
    if (!hasAccess) {
      return res.status(401).json({
        message:
          "Unauthorized access: User does not have the required permissions",
      });
    }
    if (!uuidValidate(biblioteca_id)) {
      return res.status(400).json({ message: "Invalid biblioteca_id" });
    }

    const biblioteca = await Biblioteca.findByPk(biblioteca_id);
    if (!biblioteca) {
      return res.status(404).json({ message: "Biblioteca not found" });
    }
    const infraestructuraRecurso = await InfraestructuraRecurso.findOne({
      where: { anio_proceso: biblioteca.anio_proceso },
    });
    // If the infarestructuraRecurso is already finalized, then the biblioteca can't be deleted
    if (infraestructuraRecurso.is_finalized == true) {
      return res.status(403).json({
        message:
          "Can't delete biblioteca of an already validated Infraestructura",
      });
    }

    await biblioteca.destroy();
    res.status(200).json({ message: "Biblioteca deleted successfully" });
  } catch (error) {
    console.error("Error deleting biblioteca:", error);
    res.status(500).json({ message: "Error deleting biblioteca" });
  }
};
