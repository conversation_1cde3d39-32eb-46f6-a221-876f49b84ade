import { checkAtributosNecesarios } from "../../utils/checkAtributosNecesarios.js";
import uuidValidate from "uuid-validate";
import InmuebleRestringido from "../../models/Oti/inmuebleRestringido.js";
import Usuario from "../../models/Usuario/usuario.js";
import Acceso from "../../models/Usuario/acceso.js";
import { Op } from "sequelize";
import InfraestructuraRecurso from "../../models/Oti/InfraestructuraRecurso.js";
import { checkTokenLocal } from "../../auth/auth.js";
import validateNumbers from "../../utils/validateNumberEqualGreatZero.js";
import { sequelize } from "../../database/database.js";
import { checkPermisosUsuario } from "../../utils/checkPermisosUsuario.js";

const checkDatabase = async () => {
  try {
    console.log("Checking database connection...");
    await sequelize.authenticate();
    console.log("Database is connected successfully.");
  } catch (error) {
    console.error("Database connection error:", error);
    return res.status(400).json({ message: "Database not connected" });
  }
};

/**
 * Handler to fetch an "inmuebleRestringido" given its ID.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const getInmuebleRestringido = async (req, res) => {
  const userToken = req.headers.authorization;
  const { inmuebleRestringido_id } = req.params; // Assuming the ID is passed as a URL parameter

  try {
    // Check the database connection
    await checkDatabase();
    // Validate the UUID
    if (!uuidValidate(inmuebleRestringido_id)) {
      return res.status(400).json({ error: "Invalid UUID provided." });
    }
    // check if the token is valid and not expired
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    // Fetch the inmuebleRestringido by ID
    const inmueble = await InmuebleRestringido.findOne({
      where: { inmuebleRestringido_id: inmuebleRestringido_id },
    });

    // If the inmuebleRestringido is not found
    if (!inmueble) {
      return res.status(404).json({ error: "InmuebleRestringido not found." });
    }

    // Return the found inmuebleRestringido
    return res.status(200).json(inmueble);
  } catch (error) {
    console.error("Error fetching inmuebleRestringido:", error);
    return res.status(500).json({
      error: "An error occurred while fetching the inmuebleRestringido.",
    });
  }
};

/**
 * Handler to fetch all "inmuebleRestringido" from the database based on the infraestructuraRecurso_id.
 * Checks if the provided UUID is valid and if the InfraestructuraRecurso model contains matching data.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const getAllInmuebleRestringido = async (req, res) => {
  const userToken = req.headers.authorization;
  const { infraestructuraRecurso_id } = req.params; // ID passed as a URL parameter

  try {
    // Check the database connection
    await checkDatabase();
    // Validate the UUID
    if (!uuidValidate(infraestructuraRecurso_id)) {
      return res.status(400).json({ error: "Invalid UUID provided." });
    }
    // check if the token is valid and not expired
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    // Check if the InfraestructuraRecurso exists with the provided ID
    const infraestructuraRecurso = await InfraestructuraRecurso.findOne({
      where: { infraestructuraRecurso_id: infraestructuraRecurso_id },
    });

    if (!infraestructuraRecurso) {
      return res
        .status(404)
        .json({ error: "InfraestructuraRecurso not found." });
    }

    // Fetch all inmuebleRestringido entries related to the infraestructuraRecurso_id
    const inmuebles = await InmuebleRestringido.findAll({
      where: { infraestructuraRecurso_id },
    });

    // If no inmuebleRestringido records are found
    if (!inmuebles || inmuebles.length === 0) {
      return res.status(404).json({
        error:
          "No inmuebleRestringido records found for the given infraestructuraRecurso_id.",
      });
    }

    // Return the found inmuebleRestringido records
    return res.status(200).json(inmuebles);
  } catch (error) {
    console.error("Error fetching inmuebleRestringido records:", error);
    return res.status(500).json({
      error:
        "An error occurred while fetching the inmuebleRestringido records.",
    });
  }
};

/**
 * Handler to create an "inmuebleRestringido" in the database.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const postInmuebleRestringido = async (req, res) => {
  const {
    tipo_infraestructura,
    nombre_identificacion,
    comuna,
    direccion_inmueble,
    ur_desc_actividades,
    ur_total_m2_terreno,
    ur_total_m2_construidos,
    ur_situacion_tenencia,
    ur_desc_tenencia_otra,
    vigencia,
    anio_proceso,
    creador_email,
    creador_id,
    userToken,
  } = req.body;

  // Define required attributes
  const requiredAttributes = [
    "tipo_infraestructura",
    "nombre_identificacion",
    "comuna",
    "direccion_inmueble",
    "ur_desc_actividades",
    "ur_total_m2_terreno",
    "ur_total_m2_construidos",
    "ur_situacion_tenencia",
    "anio_proceso",
    "vigencia",
    "creador_email",
    "userToken",
  ];

  try {
    // Check the database connection
    await checkDatabase();
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredAttributes
    );
    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }

    // Check if integer values are valid numbers
    const integerFields = {
      ur_total_m2_terreno,
      ur_total_m2_construidos,
      ur_situacion_tenencia,
      anio_proceso,
      vigencia,
    };

    for (const [key, value] of Object.entries(integerFields)) {
      if (!Number.isInteger(value)) {
        return res.status(400).json({
          message: `${key} must be a valid integer.`,
        });
      }
    }

    var areNumbersValid = await validateNumbers([
      ur_total_m2_terreno,
      ur_total_m2_construidos,
      ur_situacion_tenencia,
      anio_proceso,
      vigencia,
    ]);
    if (areNumbersValid == false) {
      return res.status(400).json({ message: "Invalid number format" });
    }
    if (vigencia != 0 && vigencia != 1) {
      return res.status(400).json({ message: "Invalid vigencia value" });
    }
    // Validate that creador_email exists in the Usuario model and matches the creador_id (case-insensitive using Op.iLike)
    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: creador_email },
      },
    });

    if (!usuario) {
      return res.status(404).json({
        message:
          "User not found or does not have access to the specified resource",
      });
    }
    // Validate the user's roles
    const validRoles = [
      "Encargado",
      "Usuario General",
      "Administrador Sistema",
    ];

    const hasAccess = await checkPermisosUsuario(
      creador_email,
      validRoles,
      "OTI"
    );
    if (!hasAccess) {
      return res.status(401).json({
        message:
          "Unauthorized access: User does not have the required permissions",
      });
    }


    // Validate that infraestructura recurso exists
    const infraestructuraRecurso = await InfraestructuraRecurso.findOne({
      where: { anio_proceso: anio_proceso },
    });

    if (!infraestructuraRecurso) {
      return res.status(404).json({
        message: "infraestructuraRecurso not found",
      });
    }
    // If the infarestructuraRecurso is already finalized, then the inmuebleRestringido can't be created
    if (infraestructuraRecurso.is_finalized == true) {
      return res.status(403).json({
        message:
          "Can't create a inmueble restringido of a validated Infraestructura",
      });
    }

    var ur_situacion_condicional = ur_desc_tenencia_otra;

    if (ur_situacion_tenencia == 1) {
      ur_situacion_condicional = null;
    } else {
      if (ur_desc_tenencia_otra == null || ur_desc_tenencia_otra == "") {
        return res.status(400).json({
          message: "ur_desc_tenencia_otra is required",
        });
      }
    }

    // Create the inmuebleRestringido record
    const newInmuebleRestringido = await InmuebleRestringido.create({
      tipo_infraestructura: 2,
      nombre_identificacion,
      comuna,
      direccion_inmueble,
      ur_desc_actividades,
      ur_total_m2_terreno,
      ur_total_m2_construidos,
      ur_situacion_tenencia,
      ur_desc_tenencia_otra: ur_situacion_condicional,
      anio_proceso,
      vigencia,
      creador_email,
      creador_id: usuario.usuario_id,
      infraestructuraRecurso_id:
        infraestructuraRecurso.infraestructuraRecurso_id,
    });

    // Return the created record
    res.status(201).json(newInmuebleRestringido);
  } catch (error) {
    console.error("Error creating inmuebleRestringido:", error);
    res.status(500).json({ message: "Error creating inmuebleRestringido" });
  }
};

/**
 * Handler to edit an "inmuebleRestringido" in the database.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const editInmuebleRestringido = async (req, res) => {
  const { inmuebleRestringido_id } = req.params; // ID from the URL parameter
  const {
    tipo_infraestructura,
    nombre_identificacion,
    comuna,
    direccion_inmueble,
    ur_desc_actividades,
    ur_total_m2_terreno,
    ur_total_m2_construidos,
    ur_situacion_tenencia,
    ur_desc_tenencia_otra,
    anio_proceso,
    vigencia,
    creador_email,
    userToken,
    version_lock,
  } = req.body; // Data from the request body

  try {
    // Check the database connection
    await checkDatabase();
    // Validate the UUID
    if (!uuidValidate(inmuebleRestringido_id)) {
      return res.status(400).json({ error: "Invalid UUID provided." });
    }
    var areNumbersValid = await validateNumbers([
      ur_total_m2_terreno,
      ur_total_m2_construidos,
      ur_situacion_tenencia,
      anio_proceso,
      vigencia,
    ]);
    if (areNumbersValid == false) {
      return res.status(400).json({ message: "Invalid number format" });
    }

    const inmuebleRestringido = await InmuebleRestringido.findOne({
      where: { inmuebleRestringido_id: inmuebleRestringido_id },
    });
    const infraestructuraRecurso = await InfraestructuraRecurso.findOne({
      where: { anio_proceso: inmuebleRestringido.anio_proceso },
    });
    // If the infarestructuraRecurso is already finalized, then the inmuebleRestringido can't be updated
    if (infraestructuraRecurso.is_finalized == true) {
      return res.status(403).json({
        message:
          "Can't update a inmueble restringido of a validated Infraestructura",
      });
    }
    var ur_situacion_condicional = ur_desc_tenencia_otra;

    if (ur_situacion_tenencia == 1) {
      ur_situacion_condicional = null;
    }
    if (vigencia != 0 && vigencia != 1) {
      return res.status(400).json({ message: "Invalid vigencia value" });
    }

    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }

    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: creador_email },
      },
    });

    if (!usuario) {
      return res.status(404).json({
        message:
          "User not found or does not have access to the specified resource",
      });
    }

    // Validate the user's roles
    const validRoles = [
      "Encargado",
      "Usuario General",
      "Administrador Sistema",
    ];

    const hasAccess = await checkPermisosUsuario(
      creador_email,
      validRoles,
      "OTI"
    );
    if (!hasAccess) {
      return res.status(401).json({
        message:
          "Unauthorized access: User does not have the required permissions",
      });
    }

    if (inmuebleRestringido.version_lock != version_lock) {
      return res.status(409).json({
        message: "El inmueble restringido fue modificado por otro usuario",
      });
    }
    // Find and update the inmuebleRestringido entry
    const result = await InmuebleRestringido.update(
      {
        nombre_identificacion,
        comuna,
        direccion_inmueble,
        ur_desc_actividades,
        ur_total_m2_terreno,
        ur_total_m2_construidos,
        ur_situacion_tenencia,
        ur_desc_tenencia_otra: ur_situacion_condicional,
        anio_proceso,
        vigencia,
        version_lock: version_lock + 1,
      },
      { where: { inmuebleRestringido_id: inmuebleRestringido_id } }
    );

    // If no records were updated (i.e., record not found)
    if (result[0] === 0) {
      return res.status(404).json({ error: "InmuebleRestringido not found." });
    }

    // Respond with success message
    return res
      .status(200)
      .json({ message: "InmuebleRestringido updated successfully." });
  } catch (error) {
    console.error("Error updating inmuebleRestringido:", error);
    return res.status(500).json({
      error: "An error occurred while updating the inmuebleRestringido.",
    });
  }
};

/**
 * Handler to delete an "inmuebleRestringido" from the database.
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
export const deleteInmuebleRestringido = async (req, res) => {
  const { inmuebleRestringido_id } = req.params;
  const { userEmail, userToken } = req.body;

  try {
    // Check the database connection
    await checkDatabase();
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    // Validate the UUID
    if (!uuidValidate(inmuebleRestringido_id)) {
      return res.status(400).json({ error: "Invalid UUID provided." });
    }
    // Validate that creador_email exists in the Usuario model (case-insensitive)
    const usuario = await Usuario.findOne({
      where: {
        email: { [Op.iLike]: userEmail },
      },
      include: {
        model: Acceso,
        as: "accesos",
        where: { recurso: ["OTI", "Administracion PIU"] },
      },
    });
    if (!usuario) {
      return res.status(404).json({
        message:
          "User not found or does not have access to the specified resource",
      });
    }

    // Validate the user's roles
    const validRoles = [
      "Encargado",
      "Usuario General",
      "Administrador Sistema",
    ];

    const hasAccess = await checkPermisosUsuario(
      userEmail,
      validRoles,
      "OTI"
    );
    if (!hasAccess) {
      return res.status(401).json({
        message:
          "Unauthorized access: User does not have the required permissions",
      });
    }

    const inmuebleRestringido = await InmuebleRestringido.findOne({
      where: { inmuebleRestringido_id: inmuebleRestringido_id },
    });
    const infraestructuraRecurso = await InfraestructuraRecurso.findOne({
      where: { anio_proceso: inmuebleRestringido.anio_proceso },
    });
    // If the infarestructuraRecurso is already finalized, then the inmuebleRestringido can't be deleted
    if (infraestructuraRecurso.is_finalized == true) {
      return res.status(403).json({
        message:
          "Can't delete a inmueble restringido of a validated Infraestructura",
      });
    }

    // Attempt to delete the inmuebleRestringido
    const result = await InmuebleRestringido.destroy({
      where: { inmuebleRestringido_id: inmuebleRestringido_id },
    });

    // Check if a record was deleted
    if (result === 0) {
      return res.status(404).json({ error: "InmuebleRestringido not found." });
    }

    // Return a success response
    return res
      .status(200)
      .json({ message: "InmuebleRestringido deleted successfully." });
  } catch (error) {
    console.error("Error deleting inmuebleRestringido:", error);
    return res.status(500).json({
      error: "An error occurred while deleting the inmuebleRestringido.",
    });
  }
};
