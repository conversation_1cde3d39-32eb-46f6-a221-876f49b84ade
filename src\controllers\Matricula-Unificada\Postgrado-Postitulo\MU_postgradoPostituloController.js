import { checkAtributosNecesarios } from "../../../utils/checkAtributosNecesarios.js";
import { Op } from "sequelize";
import uuidValidate from "uuid-validate";
import { sequelize } from "../../../database/database.js";
import { Sequelize } from "sequelize";
import { checkTokenLocal, getEmailFromToken } from "../../../auth/auth.js";
import MU_postgrado_postitulo_anio_proceso from "../../../models/Matricula-Unificada/postgrado-postitulo/mu_postgrado_postitulo_anio_proceso.js";
import MU_postgrado_postitulo_estudiante from "../../../models/Matricula-Unificada/postgrado-postitulo/mu_postgrado_postitulo_estudiante.js";
import MU_postgrado_postitulo_fecha from "../../../models/Matricula-Unificada/postgrado-postitulo/mu_postgrado_postitulo_fecha.js";
import Usuario from "../../../models/Usuario/usuario.js";
import { sendTestEmail } from "../../../utils/emailTest.js";
import Papa from "papaparse";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import iconv from "iconv-lite";

// VALIDATED
const checkDatabase = async () => {
  try {
    console.log("Checking database connection...");
    await sequelize.authenticate();
    console.log("Database is connected successfully.");
  } catch (error) {
    console.error("Database connection error:", error);
    return res.status(400).json({ message: "Database not connected" });
  }
};
// VALIDATED
export const getAllMU_postgrado = async (req, res) => {
  try {
    const userToken = req.headers.authorization;
    // Check the database connection
    await checkDatabase();
    // Check if the token is valid and not expired
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }

    const matriculas = await MU_postgrado_postitulo_anio_proceso.findAll({
      attributes: {
        include: [
          [
            Sequelize.fn(
              "COUNT",
              Sequelize.col("MU_postgrado_postitulo_estudiantes.mu_postgrado_postitulo_estudiante_id")
            ),
            "estudiante_count",
          ],
        ],
      },
      include: [
        {
          model: MU_postgrado_postitulo_estudiante,
          attributes: [], // without attributes
        },
      ],
      group: ["MU_postgrado_postitulo_anio_proceso.mu_postgrado_postitulo_anio_proceso_id"],
      raw: true,
    });

    if (!matriculas || matriculas.length === 0) {
      return res.status(404).json({ message: "No data found" });
    }

    // Add the array of MU_postgrado_postitulo_fecha and its length to each element
    for (const matricula of matriculas) {
      const fechas = await MU_postgrado_postitulo_fecha.findAll({
        where: {
          mu_postgrado_postitulo_anio_proceso_id: matricula.mu_postgrado_postitulo_anio_proceso_id,
        },
        raw: true,
      });
      matricula.fecha_etapa_count = fechas.length; // Add the length of fechas
    }

    // Convert estudiante_count to integer
    matriculas.forEach((matricula) => {
      matricula.estudiante_count = parseInt(matricula.estudiante_count, 10);
    });

    res.status(200).json(matriculas);
  } catch (error) {
    console.error("Error while fetching data:", error);
    res.status(500).json({ error: "Se detecto un error al obtener los datos", details: error.message });
  }
};
// VALIDATED
export const getMU_postgrado = async (req, res) => {
  try {
    const { anio_proceso } = req.params;
    const userToken = req.headers.authorization;
    await checkDatabase();
    // check if the token is valid and not expired
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    const matricula = await MU_postgrado_postitulo_anio_proceso.findOne({
      where: { anio_proceso },
    });

    if (matricula) {
      // Initialize an array to store the count of students for each etapa
      const etapaCounts = [];

      // Loop through each etapa up to the current etapa_actual
      for (let etapa = 1; etapa <= matricula.etapa_actual; etapa++) {
        const count = await MU_postgrado_postitulo_estudiante.count({
          where: {
            mu_postgrado_postitulo_anio_proceso_id: matricula.mu_postgrado_postitulo_anio_proceso_id,
            etapa_proceso: etapa,
          },
        });
        etapaCounts.push({ etapa, count });
      }

      // Fetch and add MU_postgrado_postitulo_fecha as an array to matricula

      const fechas = await MU_postgrado_postitulo_fecha.findAll({
        where: {
          mu_postgrado_postitulo_anio_proceso_id: matricula.mu_postgrado_postitulo_anio_proceso_id,
        },
        order: [
          ["anio_proceso", "ASC"],
          ["etapa_proceso", "ASC"],
        ],
        raw: true,
      });

      const matriculaObject = matricula.toJSON();
      matriculaObject.etapaCounts = etapaCounts;
      matriculaObject.fechas = fechas;

      if (matriculaObject) {
        res.status(200).json(matriculaObject);
      } else {
        res.status(404).json({ error: "Matricula posgrado-postitulo not found" });
      }
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};
// VALIDATED
export const postMU_postgrado = async (req, res) => {
  try {
    const userToken = req.headers.authorization;
    const { anio_proceso, fecha_inicio, fecha_termino } = req.body;
    // Check the database connection
    await checkDatabase();
    // check if the token is valid and not expired
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    const creador_email = await getEmailFromToken(userToken);
    console.log(creador_email);

    const creador = await Usuario.findOne({
      where: { email: { [Op.iLike]: creador_email } },
    });
    if (!creador) {
      return res.status(404).json({ message: "Usuario not found" });
    }
    // Check if the anio_proceso already exists
    const existingMatricula = await MU_postgrado_postitulo_anio_proceso.findOne({
      where: { anio_proceso },
    });
    if (existingMatricula) {
      return res.status(400).json({ message: "Anio_proceso already exists" });
    }
    const transaction = await sequelize.transaction();
    try {
      const newMatricula = await MU_postgrado_postitulo_anio_proceso.create(
        {
          creador_id: creador.usuario_id,
          anio_proceso,
          creador_email,
        },
        { transaction }
      );

      const newFecha = await MU_postgrado_postitulo_fecha.create(
        {
          mu_postgrado_postitulo_anio_proceso_id: newMatricula.mu_postgrado_postitulo_anio_proceso_id,
          fecha_inicio,
          fecha_termino,
          anio_proceso,
          etapa_proceso: 1,
          creador_email,
        },
        { transaction }
      );

      await transaction.commit();
      res.status(201).json({ newMatricula, newFecha });
    } catch (error) {
      await transaction.rollback();
      res.status(500).json({
        message: "Error creating Matricula or Fecha",
        error: error.message,
      });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// VALIDATED
export const confirm_MU_PP = async (req, res) => {
  const userToken = req.headers.authorization;
  const { anio_proceso } = req.body;

  // Define required attributes
  const requiredAttributes = ["anio_proceso"];

  try {
    // Check the database connection
    await checkDatabase();
    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredAttributes
    );
    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }

    // Cast anio_proceso to a number and check if it's an integer
    const anioProcesoNumber = Number(anio_proceso);
    if (!Number.isInteger(anioProcesoNumber)) {
      return res
        .status(400)
        .json({ message: "anio_proceso must be an integer" });
    }
    // Check if an MU_postgrado_postitulo with the same anio_proceso already exists
    const existing_MU_PP = await MU_postgrado_postitulo_anio_proceso.findOne({
      where: { anio_proceso: anioProcesoNumber },
    });
    if (!existing_MU_PP) {
      return res.status(404).json({
        message: "No se encontró un registro de Matricula Unificada postitulo-posgrado para el año de proceso proporcionado.",
      });
    }

    const creador_email = await getEmailFromToken(userToken);
    console.log(creador_email);

    const creador = await Usuario.findOne({
      where: { email: { [Op.iLike]: creador_email } },
    });
    if (!creador) {
      return res.status(404).json({ message: "Usuario not found" });
    }

    // Validate the user's roles
    /*const validRoles = ["Encargado", "Administrador Sistema"];

      const hasAccess = await checkPermisosUsuario(
        creador.email,
        validRoles,
        "OTI"
      );
      if (!hasAccess) {
        return res.status(401).json({
          message:
            "Unauthorized access: User does not have the required permissions",
        });
      }*/

    // Get the specific data from MU_postgrado_postitulo_anio_proceso using anio_proceso
    const muPosgradoPostitulo = await MU_postgrado_postitulo_anio_proceso.findOne({
      where: { anio_proceso: anioProcesoNumber },
    });

    if (!muPosgradoPostitulo) {
      return res
        .status(404)
        .json({ message: "Matricula Unificada postitulo-posgrado record not found" });
    }

    // Update the is_finalized fields
    let isFinalized = muPosgradoPostitulo.is_finalized;

    // Since there is only one etapa, finalize directly
    isFinalized = true;

    // Update the record
    const [updated] = await MU_postgrado_postitulo_anio_proceso.update(
      {
        is_finalized: isFinalized,
        [`fecha_validacion_1`]: new Date(),
      },
      { where: { anio_proceso: anioProcesoNumber } }
    );

    // Update the record in MU_postgrado_postitulo_fechas
    const [updatedFecha] = await MU_postgrado_postitulo_fecha.update(
      {
        validated_by: creador.email,
      },
      {
        where: {
          anio_proceso: anioProcesoNumber,
          etapa_proceso: etapaActual,
        },
      }
    );

    if (updated && updatedFecha) {
      // Send email if update is successful
      try {
        // Send email if update is successful
        await sendTestEmail(
          "<EMAIL>",
          "Validación Matricula Unificada postitulo-posgrado proceso " +
            anioProcesoNumber,
          "Se realizo la validacion de la Matricula Unificada postitulo-posgrado del proceso " +
            anioProcesoNumber +
            " por el usuario " +
            creador.email
        );
      } catch (emailError) {
        console.error("Error sending email:", emailError);
        return res.status(500).json({
          message: "Update successful, but failed to send email",
          error: emailError.message,
        });
      }

      // Return the updated record
      const updated_MU_PP = await MU_postgrado_postitulo_anio_proceso.findOne({
        where: { anio_proceso: anioProcesoNumber },
      });
      return res.status(201).json(updated_MU_PP);
    } else {
      return res
        .status(404)
        .json({ message: "Matricula Unificada postitulo-posgrado record not found" });
    }
  } catch (error) {
    console.error("Error creating Matricula Unificada postitulo-posgrado:", error);
    res
      .status(500)
      .json({ message: "Error creating Matricula Unificada postitulo-posgrado" });
  }
};

// VALIDATED
export const deleteMU_postgrado = async (req, res) => {
  try {
    const { anio_proceso } = req.params;
    const userToken = req.headers.authorization;
    await checkDatabase();
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    // Find the MU_postgrado_postitulo_anio_proceso by anio_proceso
    const muPosgradoPostitulo = await MU_postgrado_postitulo_anio_proceso.findOne({
      where: { anio_proceso },
    });

    if (!muPosgradoPostitulo) {
      return res.status(404).json({ error: "Matricula postitulo-posgrado not found" });
    }

    const transaction = await sequelize.transaction();
    try {
      // Find and delete associated MU_postgrado_postitulo_fecha records
      const deletedFechas = await MU_postgrado_postitulo_fecha.destroy({
        where: {
          mu_postgrado_postitulo_anio_proceso_id: muPosgradoPostitulo.mu_postgrado_postitulo_anio_proceso_id,
        },
        transaction,
      });

      // Delete the MU_postgrado_postitulo_anio_proceso record
      const deletedMatricula = await MU_postgrado_postitulo_anio_proceso.destroy({
        where: { anio_proceso },
        transaction,
      });

      await transaction.commit();

      if (deletedFechas && deletedMatricula) {
        res.status(204).send();
      } else {
        res.status(404).json({
          error: "Error deleting Matricula postitulo-posgrado or associated fechas",
        });
      }
    } catch (error) {
      await transaction.rollback();
      console.error("Error deleting Matricula Unificada postitulo-posgrado:", error);

      // Check if the error is a foreign key constraint error (ON DELETE RESTRICT)
      if (error instanceof Sequelize.ForeignKeyConstraintError) {
        return res.status(409).json({
          message:
            "Cannot delete Matricula Unificada postitulo-posgrado: It is referenced by other records.",
        });
      }
      // For other types of errors
      res
        .status(500)
        .json({ message: "Error deleting Matricula Unificada postitulo-posgrado" });
    }
  } catch (error) {
    console.error("Error deleting Matricula Unificada postitulo-posgrado:", error);

    // Check if the error is a foreign key constraint error (ON DELETE RESTRICT)
    if (error instanceof Sequelize.ForeignKeyConstraintError) {
      return res.status(409).json({
        message:
          "Cannot delete Matricula Unificada postitulo-posgrado: It is referenced by other records.",
      });
    }
    // For other types of errors
    res
      .status(500)
      .json({ message: "Error deleting Matricula Unificada postitulo-posgrado" });
  }
};

// controller para fechas etapas
export const getAllFechas_postgrado = async (req, res) => {
  try {
    const userToken = req.headers.authorization;
    await checkDatabase();
    // check if the token is valid and not expired
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    const fechas = await MU_postgrado_postitulo_fecha.findAll();

    if (fechas) {
      res.status(200).json(fechas);
    } else {
      res.status(404).json({ error: "Fechas not found" });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

export const getFechas_postgrado = async (req, res) => {
  try {
    const { anio_proceso } = req.params;
    const userToken = req.headers.authorization;
    await checkDatabase();
    // check if the token is valid and not expired
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    const matricula = await MU_postgrado_postitulo_anio_proceso.findOne({
      where: { anio_proceso },
    });

    if (matricula) {
      const fechas = await MU_postgrado_postitulo_fecha.findAll({
        where: { anio_proceso },
      });

      if (fechas) {
        res.status(200).json(fechas);
      } else {
        res.status(404).json({ error: "Fechas not found" });
      }
    } else {
      res.status(404).json({ error: "Matricula postitulo-posgrado not found" });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

export const postFechas_postgrado = async (req, res) => {
  const { anio_proceso, fecha_inicio, fecha_termino, etapa_actual } = req.body;
  const userToken = req.headers.authorization;
  try {
    await checkDatabase();
    // check if the token is valid and not expired
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    const creador_email = await getEmailFromToken(userToken);
    console.log(creador_email);

    const creador = await Usuario.findOne({
      where: { email: { [Op.iLike]: creador_email } },
    });
    if (!creador) {
      return res.status(404).json({ message: "Usuario not found" });
    }

    const muPosgradoPostitulo = await MU_postgrado_postitulo_anio_proceso.findOne({
      where: { anio_proceso },
    });

    if (!muPosgradoPostitulo) {
      return res.status(404).json({ message: "Matricula postitulo-posgrado not found" });
    }
    if (muPosgradoPostitulo.etapa_actual > etapa_actual) {
      return res.status(400).json({
        message: "Cannot modify Matricula postitulo-posgrado: It is finalized",
      });
    }
    const existingFecha = await MU_postgrado_postitulo_fecha.findOne({
      where: {
        mu_postgrado_postitulo_anio_proceso_id: muPosgradoPostitulo.mu_postgrado_postitulo_anio_proceso_id,
        etapa_proceso: etapa_actual,
      },
    });

    if (existingFecha) {
      // Update the existing fecha
      existingFecha.fecha_inicio = fecha_inicio;
      existingFecha.fecha_termino = fecha_termino;
      await existingFecha.save();
      res
        .status(200)
        .json({ message: "Fecha updated successfully", existingFecha });
    } else {
      // Create a new fecha
      const newFecha = await MU_postgrado_postitulo_fecha.create({
        mu_postgrado_postitulo_anio_proceso_id: muPosgradoPostitulo.mu_postgrado_postitulo_anio_proceso_id,
        fecha_inicio,
        fecha_termino,
        anio_proceso,
        etapa_proceso: etapa_actual,
        creador_email,
      });
      res.status(201).json({ message: "Fecha created successfully", newFecha });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

export const postEstudiantesPpFromCSV = async (req, res) => {
  const userToken = req.headers.authorization;
  const { anio_proceso, etapa_actual } = req.params;
  const { estudiantes } = req.body; // Assuming the CSV data is sent in the request body
  try {
    // Check the database connection
    await checkDatabase();
    // check if the token is valid and not expired
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    const creador_email = await getEmailFromToken(userToken);
    const creador = await Usuario.findOne({
      where: { email: { [Op.iLike]: creador_email } },
    });
    if (!creador) {
      return res.status(404).json({ message: "Usuario not found" });
    }
    //TODO: READ the real OA DATA FROM the database using the "anio_proceso"
    const dataOA = await read_OA_Data();
    // Filter and print cod_tipo_plan_carrera where value is 3
    const planCarrera3 = dataOA.filter(
      (item) => item.cod_tipo_plan_carrera === "3"
    );

    // Count total of cod_tipo_plan_carrera that matches values 1, 2, and 3
    const planCarrera1 = dataOA.filter(
      (item) => item.cod_tipo_plan_carrera === "1"
    );
    const planCarrera2 = dataOA.filter(
      (item) => item.cod_tipo_plan_carrera === "2"
    );
    console.log(
      "Count of cod_tipo_plan_carrera with value 1:",
      planCarrera1.length
    );
    console.log(
      "Count of cod_tipo_plan_carrera with value 2:",
      planCarrera2.length
    );
    console.log(
      "Count of cod_tipo_plan_carrera with value 3:",
      planCarrera3.length
    );
    const validationResult = validateStructureEstudiantes(
      estudiantes,
      anio_proceso,
      etapa_actual,
      dataOA
    );

    if (validationResult === true) {
      const transaction = await sequelize.transaction();
      try {
        // Convert the name of the attributes of estudiantes to lowercase
        const lowercaseEstudiantes = estudiantes.map((estudiante) => {
          return Object.keys(estudiante).reduce((acc, key) => {
            if (key === "FECH_NAC" || key === "FECHA_MATRICULA") {
              const [day, month, year] = estudiante[key].split("/");
              acc[key.toLowerCase()] = `${year}-${month}-${day}`;
            } else {
              acc[key.toLowerCase()] = estudiante[key];
            }
            return acc;
          }, {});
        });

        const currentMUPosgrado = await MU_postgrado_postitulo_anio_proceso.findOne({
          where: { anio_proceso },
        });

        if (!currentMUPosgrado) {
          throw new Error(
            "No se encontró el proceso de Matrícula Unificada postitulo-posgrado para el año y etapa actual"
          );
        }

        for (const estudiante of lowercaseEstudiantes) {
          await MU_postgrado_postitulo_estudiante.create(
            {
              ...estudiante,
              mu_postgrado_postitulo_anio_proceso_id:
                currentMUPosgrado.mu_postgrado_postitulo_anio_proceso_id,
              etapa_proceso: etapa_actual,
              creador_email,
            },
            { transaction }
          );
        }

        // Update the fecha_carga for the current etapa
        await MU_postgrado_postitulo_anio_proceso.update(
          {
            [`fecha_carga_${etapa_actual}`]: new Date(),
            upploaded_by: creador.email,
          },
          {
            where: { anio_proceso },
            transaction,
          }
        );

        await transaction.commit();
        return res.status(200).json({ message: "All correct" });
      } catch (error) {
        await transaction.rollback();
        console.error("Error creating estudiantes:", error);
        res.status(500).json({ message: "Error creating estudiantes" });
      }
    } else {
      // Create an array to hold all error rows
      let csvRows = [];

      // Add CSV header
      csvRows.push(['Tipo Doc', 'N Doc', 'DV', 'Primer Apellido', 'Segundo Apellido', 'Nombre', 'Sexo', 'Fecha Nacimiento', 'Error']);

      // Process each student's errors
      for (const studentId in validationResult.groupedErrors) {
        // Split the student ID to get individual fields
        const studentInfo = studentId.split(';');

        // For each error message for this student
        validationResult.groupedErrors[studentId].forEach(errorMsg => {
          // Create a row with student info and error message
          const row = [
            studentInfo[0] || '', // TIPO_DOC
            studentInfo[1] || '', // N_DOC
            studentInfo[2] || '', // DV
            studentInfo[3] || '', // PRIMER_APELLIDO
            studentInfo[4] || '', // SEGUNDO_APELLIDO
            studentInfo[5] || '', // NOMBRE
            studentInfo[6] || '', // SEXO
            studentInfo[7] || '', // FECH_NAC
            errorMsg           // Error message
          ];

          csvRows.push(row);
        });
      }

      // Convert the array to CSV format
      const csvContent = csvRows.map(row => row.join(';')).join('\n');

      // Encode the CSV content with ANSI (Windows-1252)
      const encodedCsvContent = iconv.encode(csvContent, 'windows-1252');

      // Send the encoded CSV to the front end with status code 400
      res.status(400);
      res.setHeader('Content-Type', 'text/csv; charset=windows-1252');
      res.setHeader('Content-Disposition', 'attachment; filename="errores_estudiantes.csv"');
      res.send(encodedCsvContent);
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};


export const exportCSVPP = async (req, res) => {
  const userToken = req.headers.authorization;
  const { anio_proceso, etapa_actual } = req.params;

  try {
    // Check the database connection
    await checkDatabase();
    // Check if the token is valid and not expired
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }

    const currentMUPosgrado = await MU_postgrado_postitulo_anio_proceso.findOne({
      where: { anio_proceso },
    });
    if (!currentMUPosgrado) {
      return res
        .status(404)
        .json({ message: "MU_postgrado_postitulo_anio_proceso not found" });
    }

    // Fetch data from the database based on anio_proceso and etapa_actual
    const dataCsvFormat = await MU_postgrado_postitulo_estudiante.findAll({
      where: {
        mu_postgrado_postitulo_anio_proceso_id:
          currentMUPosgrado.mu_postgrado_postitulo_anio_proceso_id,
        etapa_proceso: etapa_actual,
      },
      attributes: {
        exclude: [
          "MU_postgrado_postitulo_estudiante_id",
          "mu_postgrado_postitulo_anio_proceso_id",
          "createdAt",
          "updatedAt",
          "creador_email",
          "etapa_proceso",
        ],
      },
    });
    if (!dataCsvFormat) {
      return res.status(404).json({ message: "Data not found" });
    }
    // Get the headers from the first item in dataCsvFormat
    const headers = Object.keys(dataCsvFormat[0].dataValues);
    // Prepare the data for CSV
    const csvData = dataCsvFormat.map((item) => item.dataValues);

    // Function to format date from yyyy-mm-dd to dd/mm/yyyy
    const formatDate = (dateString) => {
      if (!dateString) return "";
      const [year, month, day] = dateString.split("-");
      return `${day}/${month}/${year}`;
    };

    // Create CSV content with formatted dates
    let csvContent =
      headers.map((header) => header.toUpperCase()).join(";") + "\n";
    csvContent += csvData
      .map((row) =>
        headers
          .map((header) => {
            if (
              header.toUpperCase() === "FECH_NAC" ||
              header.toUpperCase() === "FECHA_MATRICULA"
            ) {
              return formatDate(row[header]);
            }
            return row[header];
          })
          .join(";")
      )
      .join("\n");
    // Encode the CSV content with ANSI (Windows-1252)
    const encodedCsvContent = iconv.encode(csvContent, "windows-1252");

    // Send the encoded CSV to the front end
    res.setHeader("Content-Type", "text/csv; charset=windows-1252");
    res.setHeader(
      "Content-Disposition",
      'attachment; filename="estudiantes_PP_PIU.csv"'
    );
    res.send(encodedCsvContent);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};


export const deleteEstudiantesPP = async (req, res) => {
  const userToken = req.headers.authorization;
  const { anio_proceso, etapa_actual } = req.params;
  try {
    // Check the database connection
    await checkDatabase();
    // Check if the token is valid and not expired
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }
    const currentMUPosgrado = await MU_postgrado_postitulo_anio_proceso.findOne({
      where: { anio_proceso },
    });
    if (!currentMUPosgrado) {
      return res
        .status(404)
        .json({ message: "MU_postgrado_postitulo_anio_proceso not found" });
    }

    // Check if the MU_postgrado_postitulo_fechas is already validated
    const validatedFecha = await MU_postgrado_postitulo_fecha.findOne({
      where: { 
        anio_proceso, 
        etapa_proceso: etapa_actual,
        validated_by: { [Op.ne]: null }
      },
    });

    if (validatedFecha) {
      return res.status(422).json({ 
        message: "Cannot delete data. This etapa has already been validated." 
      });
    }

    const transaction = await sequelize.transaction();
    try {
      // Delete estudiantes for the current etapa
      const estudiantes_deleted = await MU_postgrado_postitulo_estudiante.destroy({
        where: {
          mu_postgrado_postitulo_anio_proceso_id:
            currentMUPosgrado.mu_postgrado_postitulo_anio_proceso_id,
          etapa_proceso: etapa_actual,
        },
        transaction,
      });

      // Set fecha_carga for the current etapa to null
      const fechas_deleted = await MU_postgrado_postitulo_anio_proceso.update(
        {
          [`fecha_carga_${etapa_actual}`]: null,
        },
        {
          where: { anio_proceso },
          transaction,
        }
      );

      const validation_user_deleted = await MU_postgrado_postitulo_fecha.update(
        {
          validated_by: null,
        },
        {
          where: { anio_proceso, etapa_proceso: etapa_actual },
          transaction,
        }
      );

      await transaction.commit();

      if (estudiantes_deleted && fechas_deleted && validation_user_deleted) {
        res.status(204).send();
      } else {
        res.status(404).json({ error: "Estudiantes not found" });
      }
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const validateStructureEstudiantes = (
  estudiantes,
  anio_proceso,
  etapa_actual,
  dataOA
) => {
  console.log(anio_proceso);
  console.log(etapa_actual);
  // Create a custom array with an overridden push method
  const errors = [];
  // Object to store errors grouped by student
  const groupedErrors = {};

  // Store the original push method
  const originalPush = errors.push;

  // Override the push method to also add to groupedErrors
  errors.push = function (...args) {
    // Get the current student ID from the closure
    const currentStudentId = this.currentStudentId;

    // Add to groupedErrors if we have a valid student ID
    if (currentStudentId && groupedErrors[currentStudentId]) {
      args.forEach((arg) => {
        groupedErrors[currentStudentId].push(arg);
      });
    }

    // Call the original push method
    return originalPush.apply(this, args);
  };

  // Helper function to add errors to both collections (for backward compatibility)
  const addError = (studentId, errorMsg) => {
    errors.currentStudentId = studentId;
    errors.push(errorMsg);
  };
  //validations
  estudiantes.forEach((estudiante, index) => {
    // Create a unique identifier for each student using the specified variables
    const studentId = `${estudiante.TIPO_DOC || ""};${estudiante.N_DOC || ""};${
      estudiante.DV || ""
    };${estudiante.PRIMER_APELLIDO || ""};${
      estudiante.SEGUNDO_APELLIDO || ""
    };${estudiante.NOMBRE || ""};${estudiante.SEXO || ""};${
      estudiante.FECH_NAC || ""
    }`;

    // Initialize the array for this student if it doesn't exist
    if (!groupedErrors[studentId]) {
      groupedErrors[studentId] = [];
    }

    // Set the current student ID for error collection
    errors.currentStudentId = studentId;
    if (
      !estudiante.COD_CAR ||
      !Number.isInteger(Number(estudiante.COD_CAR)) ||
      !estudiante.VERSION ||
      !Number.isInteger(Number(estudiante.VERSION))
    ) {
      const errorMsg = `Error en la columna ${
        index + 2
      }: "COD_CAR" o "VERSION" no existe o no es un número entero.`;
      addError(studentId, errorMsg);
    } else {
      const matchingOA = dataOA.find(
        (oa) =>
          oa.cod_carrera === estudiante.COD_CAR &&
          oa.version === estudiante.VERSION
      );

      if (matchingOA) {
        if (matchingOA.cod_nivel_global !== "1") {
          const errorMsg = `Error en la columna ${index + 2}: La carrera ${
            estudiante.COD_CAR
          } versión ${
            estudiante.VERSION
          } no corresponde a un programa de postitulo-posgrado (COD_NIVEL_GLOBAL debe ser 1).`;
          addError(studentId, errorMsg);
          // validation for each attribute
        } else {
          // TIPO_DOC
          if (
            !estudiante.TIPO_DOC ||
            typeof estudiante.TIPO_DOC !== "string" ||
            estudiante.TIPO_DOC.trim() === ""
          ) {
            const errorMsg = `Error en la columna ${
              index + 2
            }: "TIPO_DOC" se encuentra vacío o es inválido.`;
            addError(studentId, errorMsg);
          } else {
            const allowedValues = ["R", "P"];
            if (!allowedValues.includes(estudiante.TIPO_DOC)) {
              const errorMsg = `Error en la columna ${
                index + 2
              }: "TIPO_DOC" debe ser uno de los siguientes valores: ${allowedValues.join(
                ", "
              )}.`;
              addError(studentId, errorMsg);
            }
          }
          // N_DOC
          if (!estudiante.N_DOC || estudiante.N_DOC.trim() === "") {
            errors.push(
              `Error en la columna ${
                index + 2
              }: "N_DOC" se encuentra vacío o es inválido.`
            );
          } else {
            if (estudiante.N_DOC === "0" || estudiante.N_DOC.startsWith("0")) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "N_DOC" no puede ser 0 o empezar con 0.`
              );
            }
            if (!Number.isInteger(Number(estudiante.N_DOC))) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "N_DOC" debe ser un número entero.`
              );
            }
            if (estudiante.N_DOC.length > 8) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "N_DOC" no puede tener más de 8 dígitos.`
              );
            }
            if (estudiante.N_DOC.length < 3) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "N_DOC" debe tener al menos 4 dígitos.`
              );
            }
          }
          // DV
          if (estudiante.TIPO_DOC === "R") {
            // es rut
            if (!estudiante.DV || !/^[0-9K]$/.test(estudiante.DV)) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "DV" debe ser un número del 0 al 9 o la letra K.`
              );
            }
            if (isValidRut(estudiante.N_DOC, estudiante.DV) === false) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "DV" no es válido para el RUT ${estudiante.N_DOC}.`
              );
            }
          } else {
            // es pasaporte
            if (estudiante.DV && estudiante.DV.trim() !== "") {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "DV" debe estar vacío para TIPO_DOC "P".`
              );
            }
          }
          //PRIMER_APELLIDO
          if (
            !estudiante.PRIMER_APELLIDO ||
            typeof estudiante.PRIMER_APELLIDO !== "string" ||
            estudiante.PRIMER_APELLIDO.trim() === ""
          ) {
            errors.push(
              `Error en la columna ${
                index + 2
              }: "PRIMER_APELLIDO" se encuentra vacío o es inválido.`
            );
          } else {
            if (
              !/^[A-ZÄËÏÖÜÑ'\s-]+$/.test(
                estudiante.PRIMER_APELLIDO.replace(/\s/g, "")
              )
            ) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "PRIMER_APELLIDO" solo puede contener letras mayúsculas (A-Z), ÄËÏÖÜ, comilla simple y guion.`
              );
            }
            if (estudiante.PRIMER_APELLIDO.length > 50) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "PRIMER_APELLIDO" no puede tener más de 50 caracteres.`
              );
            }
            if (estudiante.PRIMER_APELLIDO.startsWith(" ")) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "PRIMER_APELLIDO" no puede iniciar con un espacio en blanco.`
              );
            }
            if (estudiante.PRIMER_APELLIDO.endsWith(" ")) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "PRIMER_APELLIDO" no puede finalizar con un espacio en blanco.`
              );
            }
            if (/\s{2,}/.test(estudiante.PRIMER_APELLIDO)) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "PRIMER_APELLIDO" no puede contener dobles o más espacios en blanco.`
              );
            }
          }
          //SEGUNDO_APELLIDO
          if (
            estudiante.SEGUNDO_APELLIDO &&
            typeof estudiante.SEGUNDO_APELLIDO === "string" &&
            estudiante.SEGUNDO_APELLIDO.trim() !== ""
          ) {
            if (
              !/^[A-ZÄËÏÖÜÑ'\s-]+$/.test(
                estudiante.SEGUNDO_APELLIDO.replace(/\s/g, "")
              )
            ) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "SEGUNDO_APELLIDO" solo puede contener letras mayúsculas (A-Z), ÄËÏÖÜ, comilla simple y guion.`
              );
            }
            if (estudiante.SEGUNDO_APELLIDO.length > 50) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "SEGUNDO_APELLIDO" no puede tener más de 50 caracteres.`
              );
            }
            if (estudiante.SEGUNDO_APELLIDO.startsWith(" ")) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "SEGUNDO_APELLIDO" no puede iniciar con un espacio en blanco.`
              );
            }
            if (estudiante.SEGUNDO_APELLIDO.endsWith(" ")) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "SEGUNDO_APELLIDO" no puede finalizar con un espacio en blanco.`
              );
            }
            if (/\s{2,}/.test(estudiante.SEGUNDO_APELLIDO)) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "SEGUNDO_APELLIDO" no puede contener dobles o más espacios en blanco.`
              );
            }
          }

          // NOMBRE
          if (
            !estudiante.NOMBRE ||
            typeof estudiante.NOMBRE !== "string" ||
            estudiante.NOMBRE.trim() === ""
          ) {
            errors.push(
              `Error en la columna ${
                index + 2
              }: "NOMBRE" se encuentra vacío o es inválido.`
            );
          } else {
            if (!/^[A-ZÄËÏÖÜÑ'\s-]+$/.test(estudiante.NOMBRE.trim())) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "NOMBRE" solo puede contener letras mayúsculas (A-Z), ÄËÏÖÜ, comilla simple, guion y espacios.`
              );
            }
            if (estudiante.NOMBRE.length > 50) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "NOMBRE" no puede tener más de 50 caracteres.`
              );
            }
            if (estudiante.NOMBRE.startsWith(" ")) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "NOMBRE" no puede iniciar con un espacio en blanco.`
              );
            }
            if (estudiante.NOMBRE.endsWith(" ")) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "NOMBRE" no puede finalizar con un espacio en blanco.`
              );
            }
            if (/\s{2,}/.test(estudiante.NOMBRE)) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "NOMBRE" no puede contener dobles o más espacios en blanco.`
              );
            }
          }
          // SEXO
          if (
            !estudiante.SEXO ||
            typeof estudiante.SEXO !== "string" ||
            estudiante.SEXO.trim() === ""
          ) {
            errors.push(
              `Error en la columna ${
                index + 2
              }: "SEXO" se encuentra vacío o es inválido.`
            );
          } else {
            const allowedValues = ["M", "H", "NB"];
            if (!allowedValues.includes(estudiante.SEXO)) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "SEXO" debe ser uno de los siguientes valores: ${allowedValues.join(
                  ", "
                )}.`
              );
            }
          }
          // FECH_NAC
          if (!estudiante.FECH_NAC || estudiante.FECH_NAC.trim() === "") {
            errors.push(
              `Error en la columna ${
                index + 2
              }: "FECH_NAC" no puede estar vacía.`
            );
          } else {
            const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;
            if (!dateRegex.test(estudiante.FECH_NAC)) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "FECH_NAC" debe tener el formato dd/mm/aaaa.`
              );
            } else {
              const [day, month, year] =
                estudiante.FECH_NAC.split("/").map(Number);
              const date = new Date(year, month - 1, day);
              const minDate = new Date(1900, 0, 1); // 01/01/1900
              if (
                date.getFullYear() !== year ||
                date.getMonth() !== month - 1 ||
                date.getDate() !== day ||
                date < minDate
              ) {
                errors.push(
                  `Error en la columna ${
                    index + 2
                  }: "FECH_NAC" no es una fecha válida o es anterior al 01/01/1900.`
                );
              }
            }
          }
          // NAC
          if (!estudiante.NAC || estudiante.NAC.trim() === "") {
            errors.push(
              `Error en la columna ${index + 2}: "NAC" no puede estar vacía.`
            );
          } else {
            if (!Number.isInteger(Number(estudiante.NAC))) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "NAC" debe ser un número entero.`
              );
            } else {
              if (Number(estudiante.NAC) < 1 || Number(estudiante.NAC) > 197) {
                errors.push(
                  `Error en la columna ${
                    index + 2
                  }: "NAC" debe estar entre 1 y 197.`
                );
              }
              if (estudiante.TIPO_DOC === "P") {
                if (estudiante.NAC === "38") {
                  errors.push(
                    `Error en la columna ${
                      index + 2
                    }: "NAC" no puede ser 38 cuando el "TIPO_DOC" es "P".`
                  );
                }
              }
            }
          }
          // PAIS_EST_SEC pais de estudios secundarios
          if (
            !estudiante.PAIS_EST_SEC ||
            estudiante.PAIS_EST_SEC.trim() === ""
          ) {
            errors.push(
              `Error en la columna ${
                index + 2
              }: "PAIS_EST_SEC" no puede estar vacía.`
            );
          } else {
            if (!Number.isInteger(Number(estudiante.PAIS_EST_SEC))) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "PAIS_EST_SEC" debe ser un número entero.`
              );
            } else {
              if (
                Number(estudiante.PAIS_EST_SEC) < 1 ||
                Number(estudiante.PAIS_EST_SEC) > 197
              ) {
                errors.push(
                  `Error en la columna ${
                    index + 2
                  }: "PAIS_EST_SEC" debe estar entre 1 y 197.`
                );
              }
            }
          }
          // COD_SED
          if (!estudiante.COD_SED || estudiante.COD_SED.trim() === "") {
            errors.push(
              `Error en la columna ${
                index + 2
              }: "COD_SED" no puede estar vacío.`
            );
          } else {
            if (!Number.isInteger(Number(estudiante.PAIS_EST_SEC))) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "COD_SED" debe ser un número entero.`
              );
            }
          }
          // COD_CAR
          if (!estudiante.COD_CAR || estudiante.COD_CAR.trim() === "") {
            errors.push(
              `Error en la columna ${
                index + 2
              }: "COD_CAR" no puede estar vacío.`
            );
          } else {
            if (!Number.isInteger(Number(estudiante.COD_CAR))) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "COD_CAR" debe ser un número entero.`
              );
            }
          }
          // MODALIDAD
          if (!estudiante.MODALIDAD || estudiante.MODALIDAD.trim() === "") {
            errors.push(
              `Error en la columna ${
                index + 2
              }: "MODALIDAD" no puede estar vacío.`
            );
          } else {
            if (!Number.isInteger(Number(estudiante.MODALIDAD))) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "MODALIDAD" debe ser un número entero.`
              );
            }
          }

          // JOR JORNADA
          if (!estudiante.JOR || estudiante.JOR.trim() === "") {
            errors.push(
              `Error en la columna ${index + 2}: "JOR" no puede estar vacío.`
            );
          } else {
            if (!Number.isInteger(Number(estudiante.JOR))) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "JOR" debe ser un número entero.`
              );
            }
          }
          // VERSION
          if (!estudiante.VERSION || estudiante.VERSION.trim() === "") {
            errors.push(
              `Error en la columna ${
                index + 2
              }: "VERSION" no puede estar vacío.`
            );
          } else {
            if (!Number.isInteger(Number(estudiante.VERSION))) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "VERSION" debe ser un número entero.`
              );
            }
          }
          // FOR_ING_ACT - Forma Ingreso Carrera Actual
          if (!estudiante.FOR_ING_ACT || estudiante.FOR_ING_ACT.trim() === "") {
            errors.push(
              `Error en la columna ${
                index + 2
              }: "FOR_ING_ACT" no puede estar vacío.`
            );
          } else {
            if (!Number.isInteger(Number(estudiante.FOR_ING_ACT))) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "FOR_ING_ACT" debe ser un número entero.`
              );
            } else {
              if (
                Number(estudiante.FOR_ING_ACT) < 1 ||
                Number(estudiante.FOR_ING_ACT) > 11
              ) {
                errors.push(
                  `Error en la columna ${
                    index + 2
                  }: "FOR_ING_ACT" debe estar entre 1 y 11.`
                );
              } else {
                if (estudiante.TIPO_DOC && estudiante.TIPO_DOC === "P") {
                  if (
                    estudiante.FOR_ING_ACT !== "4" &&
                    estudiante.FOR_ING_ACT !== "6"
                  ) {
                    errors.push(
                      `Error en la columna ${
                        index + 2
                      }: "FOR_ING_ACT" debe ser 4 o 6 cuando el "TIPO_DOC" es "P".`
                    );
                  }
                }
                if (
                  Number.isInteger(Number(estudiante.ANIO_ING_ORI)) &&
                  Number.isInteger(Number(estudiante.ANIO_ING_ACT)) &&
                  ["1", "6", "7", "8", "9", "10"].includes(
                    estudiante.FOR_ING_ACT
                  ) &&
                  estudiante.ANIO_ING_ORI !== estudiante.ANIO_ING_ACT
                ) {
                  errors.push(
                    `Error en la columna ${
                      index + 2
                    }: "ANIO_ING_ORI" y "ANIO_ING_ACT" deben ser iguales cuando "FOR_ING_ACT" es 1, 6, 7, 8, 9 o 10.`
                  );
                }
              }
            }
          }
          // ANIO_ING_ACT - Año de Ingreso Carrera Actual
          if (
            !estudiante.ANIO_ING_ACT ||
            estudiante.ANIO_ING_ACT.trim() === ""
          ) {
            errors.push(
              `Error en la columna ${
                index + 2
              }: "ANIO_ING_ACT" no puede estar vacío.`
            );
          } else {
            if (!Number.isInteger(Number(estudiante.ANIO_ING_ACT))) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "ANIO_ING_ACT" debe ser un número entero.`
              );
            } else {
              if (
                Number(estudiante.ANIO_ING_ACT) < 1990 ||
                Number(estudiante.ANIO_ING_ACT) > anio_proceso
              ) {
                errors.push(
                  `Error en la columna ${
                    index + 2
                  }: "ANIO_ING_ACT" debe estar entre 1990 y ${anio_proceso}.`
                );
              }
              if (matchingOA.cod_tipo_plan_carrera === "3") {
                if (
                  estudiante.ANIO_ING_ORI === estudiante.ANIO_ING_ACT &&
                  estudiante.SEM_ING_ORI === estudiante.SEM_ING_ACT
                ) {
                  errors.push(
                    `Error en la columna ${
                      index + 2
                    }: Cuando el tipo de plan de carrera es 3, "ANIO_ING_ORI" y "ANIO_ING_ACT" no pueden ser iguales, y "SEM_ING_ORI" y "SEM_ING_ACT" tampoco pueden ser iguales.`
                  );
                }
              }
            }
          }
          // SEM_ING_ACT - Semestre de Ingreso Carrera actual
          if (!estudiante.SEM_ING_ACT || estudiante.SEM_ING_ACT.trim() === "") {
            errors.push(
              `Error en la columna ${
                index + 2
              }: "SEM_ING_ACT" no puede estar vacío.`
            );
          } else {
            if (!Number.isInteger(Number(estudiante.SEM_ING_ACT))) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "SEM_ING_ACT" debe ser un número entero.`
              );
            } else {
              if (
                estudiante.SEM_ING_ACT !== "1" &&
                estudiante.SEM_ING_ACT !== "2"
              ) {
                errors.push(
                  `Error en la columna ${
                    index + 2
                  }: "SEM_ING_ACT" debe ser 1 o 2.`
                );
              } else {
                if (
                  ["1", "6", "7", "8", "9", "10"].includes(
                    estudiante.FOR_ING_ACT
                  )
                ) {
                  if (estudiante.SEM_ING_ORI !== estudiante.SEM_ING_ACT) {
                    errors.push(
                      `Error en la columna ${
                        index + 2
                      }: "SEM_ING_ORI" y "SEM_ING_ACT" deben ser iguales cuando "FOR_ING_ACT" es 1, 6, 7, 8, 9 o 10.`
                    );
                  }
                }
              }
            }
          }

          // ANIO_ING_ORI - Año de Ingreso Carrera de Origen
          if (
            !estudiante.ANIO_ING_ORI ||
            estudiante.ANIO_ING_ORI.trim() === ""
          ) {
            errors.push(
              `Error en la columna ${
                index + 2
              }: "ANIO_ING_ORI" no puede estar vacío.`
            );
          } else {
            if (!Number.isInteger(Number(estudiante.ANIO_ING_ORI))) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "ANIO_ING_ORI" debe ser un número entero.`
              );
            } else {
              if (
                (Number(estudiante.ANIO_ING_ORI) < 1980 ||
                  Number(estudiante.ANIO_ING_ORI) > anio_proceso) &&
                Number(estudiante.ANIO_ING_ORI) !== 1900
              ) {
                errors.push(
                  `Error en la columna ${
                    index + 2
                  }: "ANIO_ING_ORI" debe estar entre 1980 y ${anio_proceso} o ser 1900.`
                );
              } else {
                const [day, month, year] =
                  estudiante.FECH_NAC.split("/").map(Number);
                const ageAtAdmission = estudiante.ANIO_ING_ORI - year;

                if (ageAtAdmission < 15) {
                  errors.push(
                    `Error en la columna ${
                      index + 2
                    }: "ANIO_ING_ORI" menos "FECH_NAC" indica que el estudiante tiene menos de 15 años al momento de ingreso.`
                  );
                }
                if (
                  ["1", "2", "3", "6", "7", "8", "9", "10"].includes(
                    estudiante.FOR_ING_ACT
                  )
                ) {
                  if (estudiante.ANIO_ING_ORI === "1900") {
                    errors.push(
                      `Error en la columna ${
                        index + 2
                      }: "ANIO_ING_ORI" no puede ser 1900 cuando "FOR_ING_ACT" es 1, 2, 3, 6, 7, 8, 9 o 10.`
                    );
                  }
                }

                if (
                  Number(estudiante.ANIO_ING_ORI) >
                  Number(estudiante.ANIO_ING_ACT)
                ) {
                  errors.push(
                    `Error en la columna ${
                      index + 2
                    }: "ANIO_ING_ORI" no puede ser mayor al "ANIO_ING_ACT".`
                  );
                }

                if (matchingOA.cod_tipo_plan_carrera === "3") {
                  if (
                    estudiante.ANIO_ING_ORI === estudiante.ANIO_ING_ACT &&
                    estudiante.SEM_ING_ORI === estudiante.SEM_ING_ACT
                  ) {
                    errors.push(
                      `Error en la columna ${
                        index + 2
                      }: Cuando el tipo de plan de carrera es 3, "ANIO_ING_ORI" y "ANIO_ING_ACT" no pueden ser iguales, y "SEM_ING_ORI" y "SEM_ING_ACT" tampoco pueden ser iguales.`
                    );
                  }
                }
              }
            }
          }
          // SEM_ING_ORI - Semestre de ingreso Carrera de Origen
          if (!estudiante.SEM_ING_ORI || estudiante.SEM_ING_ORI.trim() === "") {
            errors.push(
              `Error en la columna ${
                index + 2
              }: "ANIO_ING_ORI" no puede estar vacío.`
            );
          } else {
            if (!Number.isInteger(Number(estudiante.SEM_ING_ORI))) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "SEM_ING_ORI" debe ser un número entero.`
              );
            } else {
              if (![0, 1, 2].includes(Number(estudiante.SEM_ING_ORI))) {
                errors.push(
                  `Error en la columna ${
                    index + 2
                  }: "SEM_ING_ORI" debe ser 0, 1 o 2.`
                );
              } else {
                // SEM_ING_ORI and SEM_ING_ACT validation
                if (
                  ["1", "6", "7", "8", "9", "10"].includes(
                    estudiante.FOR_ING_ACT
                  )
                ) {
                  if (estudiante.SEM_ING_ORI !== estudiante.SEM_ING_ACT) {
                    errors.push(
                      `Error en la columna ${
                        index + 2
                      }: "SEM_ING_ORI" y "SEM_ING_ACT" deben ser iguales cuando "FOR_ING_ACT" es 1, 6, 7, 8, 9 o 10.`
                    );
                  }
                }

                if (
                  estudiante.ANIO_ING_ORI === "1900" &&
                  estudiante.SEM_ING_ORI !== "0"
                ) {
                  errors.push(
                    `Error en la columna ${
                      index + 2
                    }: "SEM_ING_ORI" debe ser 0 cuando "ANIO_ING_ORI" es 1900.`
                  );
                }

                if (["2", "3", "4", "11"].includes(estudiante.FOR_ING_ACT)) {
                  if (
                    estudiante.ANIO_ING_ORI === estudiante.ANIO_ING_ACT &&
                    Number(estudiante.SEM_ING_ORI) >=
                      Number(estudiante.SEM_ING_ACT)
                  ) {
                    errors.push(
                      `Error en la columna ${
                        index + 2
                      }: "SEM_ING_ORI" no puede ser mayor o igual a "SEM_ING_ACT" cuando "FOR_ING_ACT" es 2, 3, 4 o 11 y "ANIO_ING_ORI" es igual a "ANIO_ING_ACT".`
                    );
                  }
                }

                if (
                  estudiante.ANIO_ING_ORI !== "1900" &&
                  estudiante.SEM_ING_ORI === "0"
                ) {
                  errors.push(
                    `Error en la columna ${
                      index + 2
                    }: "SEM_ING_ORI" no puede ser 0 cuando "ANIO_ING_ORI" es distinto de 1900.`
                  );
                }
              }
            }
          }
          // VIG - Vigencia
          if (!estudiante.VIG || estudiante.VIG.trim() === "") {
            errors.push(
              `Error en la columna ${index + 2}: "VIG" no puede estar vacío.`
            );
          } else {
            if (!["0", "1", "2"].includes(estudiante.VIG)) {
              errors.push(
                `Error en la columna ${
                  index + 2
                }: "VIG" considera valores 0: Estudiante sin matrícula, 1: Estudiante con matrícula vigente o 2: Estudiante egresado con matrícula vigente.`
              );
            }
          }
        }
      } else {
        errors.push(
          `Error en la columna ${
            index + 2
          }: No se encontró una coincidencia para COD_CAR ${
            estudiante.COD_CAR
          } y VERSION ${estudiante.VERSION} en los datos de OA.`
        );
      }
    }
  });

  // Debugging information number of students with errors
  console.log("Total errors:", errors.length);
  console.log(
    "Number of students with errors:",
    Object.keys(groupedErrors).length
  );

  // Return both the flat list of errors and the grouped errors
  return errors.length === 0 ? true : { errors, groupedErrors };
};


// VALIDATED
const read_OA_Data = async () => {
  const __dirname = path.dirname(fileURLToPath(import.meta.url));
  const filePath = path.join(__dirname, "../../../oa_data_final_2025.csv");

  try {
    const fileContent = await fs.readFileSync(filePath, { encoding: "utf8" });
    const parsedData = Papa.parse(fileContent, {
      header: true,
      skipEmptyLines: true,
      delimiter: ";",
      encoding: "utf8",
    });

    if (parsedData.errors.length > 0) {
      console.error("Errors while parsing the file:", parsedData.errors);
      return null;
    }

    // Transform the data to match the format of a "GETALL" API call
    const formattedData = parsedData.data.map((item) => ({
      cod_sede: item.COD_SEDE,
      nombre_sede: item.NOMBRE_SEDE,
      cod_carrera: item.COD_CARRERA,
      modalidad: item.MODALIDAD,
      cod_jornada: item.COD_JORNADA,
      version: item.VERSION,
      nombre_sede: item.NOMBRE_SEDE,
      cod_carrera: item.COD_CARRERA,
      modalidad: item.MODALIDAD,
      cod_jornada: item.COD_JORNADA,
      version: item.VERSION,
      cod_tipo_plan_carrera: item.COD_TIPO_PLAN_CARRERA,
      caracteristicas_tipo_plan: item.CARACTERISTICAS_TIPO_PLAN,
      duracion_estudios: item.DURACION_ESTUDIOS,
      duracion_titulacion: item.DURACION_TITULACION,
      duracion_total: item.DURACION_TOTAL,
      regimen: item.REGIMEN,
      duracion_regimen: item.DURACION_REGIMEN,
      nombre_titulo: item.NOMBRE_TITULO,
      nombre_grado: item.NOMBRE_GRADO,
      cod_nivel_global: item.COD_NIVEL_GLOBAL,
      cod_nivel_carrera: item.COD_NIVEL_CARRERA,
      cod_demre: item.COD_DEMRE,
      anio_inicio: item.ANIO_INICIO,
      acreditacion: item.ACREDITACION,
      elegible_beca_pedagogia: item.ELEGIBLE_BECA_PEDAGOGIA,
      ped_med_odont_otro: item.PED_MED_ODONT_OTRO,
      requisito_ingreso: item.REQUISITO_INGRESO,
      semestres_reconocidos: item.SEMESTRES_RECONOCIDOS,
      area_actual: item.AREA_ACTUAL,
      area_destino_agricultura: item.AREA_DESTINO_AGRICULTURA,
      area_destino_ciencias: item.AREA_DESTINO_CIENCIAS,
      area_destino_cs_sociales: item.AREA_DESTINO_CS_SOCIALES,
      area_destino_educacion: item.AREA_DESTINO_EDUCACION,
      area_destino_humanidades: item.AREA_DESTINO_HUMANIDADES,
      area_destino_salud: item.AREA_DESTINO_SALUD,
      area_destino_tecnologia: item.AREA_DESTINO_TECNOLOGIA,
      area_destino_administracion: item.AREA_DESTINO_ADMINISTRACION,
      area_destino_arte: item.AREA_DESTINO_ARTE,
      area_destino_derecho: item.AREA_DESTINO_DERECHO,
      area_destino_arquitectura: item.AREA_DESTINO_ARQUITECTURA,
      area_destino_otros: item.AREA_DESTINO_OTROS,
      otros_requisitos: item.OTROS_REQUISITOS,
      mail_difusion_carrera: item.MAIL_DIFUSION_CARRERA,
      formato_valor: item.FORMATO_VALOR,
      valor_matricula_anual: item.VALOR_MATRICULA_ANUAL,
      costo_titulacion: item.COSTO_TITULACION,
      valor_certificado_diploma: item.VALOR_CERTIFICADO_DIPLOMA,
      arancel_anual: item.ARANCEL_ANUAL,
      vigencia_carrera: item.VIGENCIA_CARRERA,
    }));

    return formattedData; // Return the formatted data as an array of objects
  } catch (error) {
    console.error("Error reading the file:", error.message);
    return null;
  }
};
// VALIDATED
const isValidRut = (rut, dv) => {
  if (!rut || !dv) return false;

  const reversedRut = rut.toString().split("").reverse();
  let sum = 0;
  let multiplier = 2;

  for (let i = 0; i < reversedRut.length; i++) {
    sum += parseInt(reversedRut[i], 10) * multiplier;
    multiplier = multiplier === 7 ? 2 : multiplier + 1;
  }

  const remainder = 11 - (sum % 11);
  const expectedDV =
    remainder === 11 ? "0" : remainder === 10 ? "K" : remainder.toString();

  // Check if the expected DV matches the provided DV (case insensitive)
  return expectedDV === dv.toString().toUpperCase();
};
