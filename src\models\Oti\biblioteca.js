import { Model, DataTypes } from "sequelize";
import sequelize from "../../database/database.js";

class Biblioteca extends Model {}

Biblioteca.init(
  {
    biblioteca_id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    tipo_infraestructura: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    nombre_identificacion: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    comuna: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    direccion_inmueble: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    total_m2_biblioteca: {
      type: DataTypes.DOUBLE,
      allowNull: false,
    },
    total_m2_salas_lectura: {
      type: DataTypes.DOUBLE,
      allowNull: false,
    },
    total_profesionales_biblioteca: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    horas_personal_biblioteca: {
      type: DataTypes.DOUBLE,
      allowNull: false,
    },
    total_titulos_disponibles: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    total_volumenes_disponibles: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    total_suscripciones_revistas: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    vigencia: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    // Sequelize optimistic lock to prevent data override
    version_lock: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
    },
    anio_proceso: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    creador_email: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    creador_id: {
      type: DataTypes.UUID,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "Biblioteca",
    freezeTableName: true,
  }
);

export default Biblioteca;
