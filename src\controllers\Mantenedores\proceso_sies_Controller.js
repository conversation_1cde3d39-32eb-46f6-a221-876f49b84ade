import { checkAtributosNecesarios } from "../../utils/checkAtributosNecesarios.js";
import uuidValidate from "uuid-validate";
import ProcesoSies from "../../models/Oferta-Academica-Reporte-Sies/proceso_sies.js";
import { Op } from "sequelize";
/**
 * Fetch all Proceso Sies
 */
export const getAllProcesoSies = async (req, res) => {
  try {
    const procesoSies = await ProcesoSies.findAll();

    if (procesoSies.length === 0) {
      return res.status(404).json({ message: "No Proceso Sies found" });
    }

    res.status(200).json(procesoSies);
  } catch (error) {
    console.error("Error fetching all Proceso Sies:", error);
    res.status(500).json({ message: "Error fetching Proceso Sies" });
  }
};

/**
 * Create a new Proceso Sies
 */
export const postProcesoSies = async (req, res) => {
  console.log(req.body); // Debugging

  const { nombre, codigo, etapa, anio_proceso, fecha_inicio, fecha_termino } =
    req.body;
  console.log(nombre, codigo, etapa, anio_proceso, fecha_inicio, fecha_termino); // Debugging

  // List of required attributes for Proceso Sies
  const requiredProcesoSiesFields = ["nombre", "codigo", "etapa"];

  try {
    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredProcesoSiesFields
    );
    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }

    // Create the new Proceso Sies record
    const newProcesoSies = await ProcesoSies.create({
      nombre,
      codigo,
      etapa,
      anio_proceso,
      fecha_inicio,
      fecha_termino,
    });
    res.status(201).json({
      message: "Proceso Sies created successfully",
      newProcesoSies,
    });
  } catch (error) {
    console.error("Error creating Proceso Sies:", error);
    res.status(500).json({ message: "Error creating Proceso Sies" });
  }
};

/**
 * Delete a specific Proceso Sies given its id.
 */
export const deleteProcesoSies = async (req, res) => {
  const { proceso_sies_id } = req.params;

  try {
    if (!uuidValidate(proceso_sies_id)) {
      return res.status(400).json({ message: "Invalid proceso_sies_id" });
    }

    const procesoSies = await ProcesoSies.findByPk(proceso_sies_id);
    if (!procesoSies) {
      return res.status(404).json({ message: "Proceso Sies not found" });
    }

    await procesoSies.destroy();
    res.status(200).json({ message: "Proceso Sies deleted successfully" });
  } catch (error) {
    console.error("Error deleting Proceso Sies:", error);
    res.status(500).json({ message: "Error deleting Proceso Sies" });
  }
};

/**
 * Get a list of Proceso Sies Oferta Academica given its year.
 */
export const getProcesoSiesOAbyYear = async (req, res) => {
  const { year } = req.params;

  try {
    const procesoSies = await ProcesoSies.findAll({
      attributes: [
        ["proceso_sies_id", "id"],
        ["anio_proceso", "anio"],
        "nombre",
        "fecha_inicio",
        "fecha_termino",
      ],
      where: {
        anio_proceso: year,
        codigo: "OA",
      },
      order: ["etapa"],
    });

    // if (procesoSies.length === 0) {
    //     return re s.status(404).json({ message: "No Proceso Sies found" });
    // }

    res.status(200).json(procesoSies);
  } catch (error) {
    console.error("Error fetching Proceso Sies:", error);
    res.status(500).json({ message: "Error fetching Proceso Sies" });
  }
};

export const getProcesoSiesOAactual = async (req, res) => {
  try {
    const fechaActual = new Date();
    const anioActual = fechaActual.getFullYear();
    const procesoSies = await ProcesoSies.findOne({
      attributes: [
        ["proceso_sies_id", "id"],
        ["anio_proceso", "anio"],
        "nombre",
        "fecha_inicio",
        "fecha_termino",
        "etapa",
      ],
      where: {
        codigo: "OA",
        anio_proceso: anioActual, // Año del proceso
        fecha_inicio: {
          [Op.lte]: fechaActual, // Inicio antes o igual a la fecha actual
        },
        fecha_termino: {
          [Op.gte]: fechaActual, // Término después o igual a la fecha actual
        },
      },
    });

    // if (procesoSies.length === 0) {
    //     return re s.status(404).json({ message: "No Proceso Sies found" });
    // }

    res.status(200).json(procesoSies);
  } catch (error) {
    console.error("Error fetching Proceso Sies:", error);
    res.status(500).json({ message: "Error fetching Proceso Sies" });
  }
};
