import { Op } from "sequelize";
import OASubProceso from "../../models/Oferta-<PERSON><PERSON>-<PERSON>/OASubProceso.js";
import OAProceso from "../../models/Oferta-<PERSON><PERSON>-<PERSON>/OAProceso.js";
import OAEtapa from "../../models/Oferta-Academic<PERSON>-<PERSON>/OAEtapa.js";
import OAOfertaAcademica from "../../models/Oferta-Academic<PERSON>-David/O<PERSON>fertaAcademica.js";
import { checkTokenLocal, getEmailFromToken } from "../../auth/auth.js";
import Usuario from "../../models/Usuario/usuario.js";
import { sequelize } from "../../database/database.js";
import { checkAtributosNecesarios } from "../../utils/checkAtributosNecesarios.js";

/**
 * Helper function to check database connection
 */
const checkDatabase = async () => {
  try {
    console.log("Checking database connection...");
    await sequelize.authenticate();
    console.log("Database is connected successfully.");
  } catch (error) {
    console.error("Database connection error:", error);
    throw new Error("Database not connected");
  }
};

/**
 * Get all OASubProcesos
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getAllOASubProcesos = async (req, res) => {
  try {
    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Fetch all subprocesos with their associated proceso
    const subprocesos = await OASubProceso.findAll({
      include: [
        {
          model: OAProceso,
          attributes: ['id', 'anio', 'fecha_inicial', 'fecha_final']
        }
      ],
      order: [["id", "ASC"]]
    });

    res.status(200).json(subprocesos);
  } catch (error) {
    console.error("Error fetching all subprocesos:", error);
    res.status(500).json({ message: "Error fetching subprocesos" });
  }
};

/**
 * Get OASubProcesos by proceso_id
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getOASubProcesosByProcesoId = async (req, res) => {
  try {
    const { proceso_id } = req.params;
    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Fetch subprocesos by proceso_id
    const subprocesos = await OASubProceso.findAll({
      where: { proceso_id },
      include: [
        {
          model: OAProceso,
          attributes: ['id', 'anio', 'fecha_inicial', 'fecha_final']
        }
      ],
      order: [["id", "ASC"]]
    });


    res.status(200).json(subprocesos);
  } catch (error) {
    console.error("Error fetching subprocesos by proceso_id:", error);
    res.status(500).json({ message: "Error fetching subprocesos" });
  }
};

/**
 * Get OASubProceso by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getOASubProcesoById = async (req, res) => {
  try {
    const { id } = req.params;
    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Find subproceso by ID
    const subproceso = await OASubProceso.findByPk(id, {
      include: [
        {
          model: OAProceso,
          attributes: ['id', 'anio', 'fecha_inicial', 'fecha_final']
        }
      ]
    });

    if (!subproceso) {
      return res.status(404).json({ message: "SubProceso not found" });
    }

    res.status(200).json(subproceso);
  } catch (error) {
    console.error("Error fetching subproceso by ID:", error);
    res.status(500).json({ message: "Error fetching subproceso" });
  }
};

/**
 * Create a new OASubProceso
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const createOASubProceso = async (req, res) => {
  try {
    const { proceso_id, tipo, fecha_inicial, fecha_final, validado } = req.body;
    const userToken = req.headers.authorization;

    // Required fields
    const requiredFields = ["proceso_id", "tipo"];

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredFields
    );

    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }

    // Verify that the proceso_id exists
    const proceso = await OAProceso.findByPk(proceso_id);
    if (!proceso) {
      return res.status(404).json({ message: "Proceso not found" });
    }

    // Get creator email from token
    const creador_email = await getEmailFromToken(userToken);

    // Find creator in database
    const creador = await Usuario.findOne({
      where: { email: { [Op.iLike]: creador_email } },
    });

    if (!creador) {
      return res.status(404).json({ message: "Usuario not found" });
    }

    // Create new subproceso
    const newSubProceso = await OASubProceso.create({
      proceso_id,
      tipo,
      fecha_inicial,
      fecha_final,
      validado: validado !== undefined ? validado : false,
    });

    // Fetch the created subproceso with its associated proceso
    const createdSubProceso = await OASubProceso.findByPk(newSubProceso.id, {
      include: [
        {
          model: OAProceso,
          attributes: ['id', 'anio', 'fecha_inicial', 'fecha_final']
        }
      ]
    });

    res.status(201).json({
      message: "SubProceso created successfully",
      subproceso: createdSubProceso,
    });
  } catch (error) {
    console.error("Error creating subproceso:", error);
    res.status(500).json({ message: "Error creating subproceso", error: error.message });
  }
};

/**
 * Update an existing OASubProceso
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const updateOASubProceso = async (req, res) => {
  try {
    const { id } = req.params;
    const { proceso_id, tipo, fecha_inicial, fecha_final, validado } = req.body;
    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Find subproceso by ID
    const subproceso = await OASubProceso.findByPk(id);

    if (!subproceso) {
      return res.status(404).json({ message: "SubProceso not found" });
    }

    // If proceso_id is being updated, verify that it exists
    if (proceso_id && proceso_id !== subproceso.proceso_id) {
      const proceso = await OAProceso.findByPk(proceso_id);
      if (!proceso) {
        return res.status(404).json({ message: "Proceso not found" });
      }
    }

    // Update subproceso
    await subproceso.update({
      proceso_id: proceso_id || subproceso.proceso_id,
      tipo: tipo || subproceso.tipo,
      fecha_inicial: fecha_inicial !== undefined ? fecha_inicial : subproceso.fecha_inicial,
      fecha_final: fecha_final !== undefined ? fecha_final : subproceso.fecha_final,
      validado: validado !== undefined ? validado : subproceso.validado,
      version_lock: subproceso.version_lock + 1, // Increment version_lock
    });

    // Fetch the updated subproceso with its associated proceso
    const updatedSubProceso = await OASubProceso.findByPk(id, {
      include: [
        {
          model: OAProceso,
          attributes: ['id', 'anio', 'fecha_inicial', 'fecha_final']
        }
      ]
    });

    res.status(200).json({
      message: "SubProceso updated successfully",
      subproceso: updatedSubProceso,
    });
  } catch (error) {
    console.error("Error updating subproceso:", error);
    res.status(500).json({ message: "Error updating subproceso", error: error.message });
  }
};

/**
 * Delete an OASubProceso
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const deleteOASubProceso = async (req, res) => {
  try {
    const { id } = req.params;
    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Find subproceso by ID
    const subproceso = await OASubProceso.findByPk(id, {
      include: [
        {
          model: OAEtapa,
          attributes: ['id']
        }
      ]
    });

    if (!subproceso) {
      return res.status(404).json({ message: "SubProceso not found" });
    }

    // Use a transaction to ensure all operations succeed or fail together
    await sequelize.transaction(async (t) => {
      // Get all etapas associated with this subproceso
      const etapas = subproceso.OAEtapas || [];

      // For each etapa, delete associated ofertas academicas first
      for (const etapa of etapas) {
        // Find all ofertas academicas associated with this etapa
        const ofertasAcademicas = await OAOfertaAcademica.findAll({
          where: { etapa_id: etapa.id },
          transaction: t
        });

        // Delete each oferta academica
        for (const oferta of ofertasAcademicas) {
          await oferta.destroy({ transaction: t });
        }

        // Delete the etapa
        await OAEtapa.destroy({
          where: { id: etapa.id },
          transaction: t
        });
      }

      // Finally, delete the subproceso
      await subproceso.destroy({ transaction: t });
    });

    res.status(200).json({ message: "SubProceso and all related records deleted successfully" });
  } catch (error) {
    console.error("Error deleting subproceso:", error);
    res.status(500).json({
      message: "Error deleting subproceso",
      error: error.message
    });
  }
};

/**
 * Create a SubProceso of tipo 1 with two etapas, each having both "insumo" and "carga" types
 * with separate dates for each etapa. The subproceso's date range spans from etapa1's start date
 * to etapa2's end date.
 *
 * @param {Object} req - Express request object with the following properties:
 *   - proceso_id: ID of the proceso to associate with the subproceso
 *   - etapa1_fecha_inicial: Start date for etapa 1 (also used as subproceso start date)
 *   - etapa1_fecha_final: End date for etapa 1
 *   - etapa2_fecha_inicial: Start date for etapa 2
 *   - etapa2_fecha_final: End date for etapa 2 (also used as subproceso end date)
 * @param {Object} res - Express response object
 */
export const createSubProceso1WithEtapas = async (req, res) => {
  try {
    const {
      proceso_id,
      tipo,
      etapa1_fecha_inicial,
      etapa1_fecha_final,
      etapa2_fecha_inicial,
      etapa2_fecha_final
    } = req.body;
    const userToken = req.headers.authorization;

    // Required fields
    const requiredFields = [
      "proceso_id",
      "tipo",
      "etapa1_fecha_inicial",
      "etapa1_fecha_final",
      "etapa2_fecha_inicial",
      "etapa2_fecha_final"
    ];

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredFields
    );

    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }

    // Verify that the proceso_id exists
    const proceso = await OAProceso.findByPk(proceso_id);
    if (!proceso) {
      return res.status(404).json({ message: "Proceso not found" });
    }

    // Get creator email from token
    const creador_email = await getEmailFromToken(userToken);

    // Find creator in database
    const creador = await Usuario.findOne({
      where: { email: { [Op.iLike]: creador_email } },
    });

    if (!creador) {
      return res.status(404).json({ message: "Usuario not found" });
    }

    // Create transaction to ensure all operations succeed or fail together
    const result = await sequelize.transaction(async (t) => {
      // Create new subproceso of tipo 1
      const newSubProceso = await OASubProceso.create({
        proceso_id,
        tipo , 
        fecha_inicial: etapa1_fecha_inicial, // Use etapa1's start date as subproceso start date
        fecha_final: etapa2_fecha_final, // Use etapa2's end date as subproceso end date
        validado: false,
        etapa_actual: 1, // Set initial etapa to 1
        creado_por: creador.email,
      }, { transaction: t });

      // Create etapas for the subproceso
      // Etapa 1 - insumo
      const etapa1Insumo = await OAEtapa.create({
        subproceso_id: newSubProceso.id,
        tipo: "insumo",
        etapa: 1,
        fecha_inicial: etapa1_fecha_inicial,
        fecha_final: etapa1_fecha_final,
        validado: false
      }, { transaction: t });

      // Etapa 1 - carga
      const etapa1Carga = await OAEtapa.create({
        subproceso_id: newSubProceso.id,
        tipo: "carga",
        etapa: 1,
        fecha_inicial: etapa1_fecha_inicial,
        fecha_final: etapa1_fecha_final,
        validado: false
      }, { transaction: t });

      // Etapa 2 - insumo
      const etapa2Insumo = await OAEtapa.create({
        subproceso_id: newSubProceso.id,
        tipo: "insumo",
        etapa: 2,
        fecha_inicial: etapa2_fecha_inicial,
        fecha_final: etapa2_fecha_final,
        validado: false
      }, { transaction: t });

      // Etapa 2 - carga
      const etapa2Carga = await OAEtapa.create({
        subproceso_id: newSubProceso.id,
        tipo: "carga",
        etapa: 2,
        fecha_inicial: etapa2_fecha_inicial,
        fecha_final: etapa2_fecha_final,
        validado: false
      }, { transaction: t });

      return {
        subproceso: newSubProceso,
        etapas: [
          etapa1Insumo,
          etapa1Carga,
          etapa2Insumo,
          etapa2Carga
        ]
      };
    });

    // Fetch the created subproceso with its associated proceso and etapas
    const createdSubProceso = await OASubProceso.findByPk(result.subproceso.id, {
      include: [
        {
          model: OAProceso,
          attributes: ['id', 'anio', 'fecha_inicial', 'fecha_final']
        },
        {
          model: OAEtapa,
          attributes: ['id', 'tipo', 'etapa', 'fecha_inicial', 'fecha_final', 'validado']
        }
      ]
    });

    res.status(201).json({
      message: "SubProceso with etapas created successfully",
      subproceso: createdSubProceso,
    });
  } catch (error) {
    console.error("Error creating subproceso with etapas:", error);
    res.status(500).json({ message: "Error creating subproceso with etapas", error: error.message });
  }
};

export const createSubProceso2WithEtapas = async (req, res) => {
  try {
    const {
      proceso_id,
      tipo,
      etapa1_fecha_inicial,
      etapa1_fecha_final,
      etapa2_fecha_inicial,
      etapa2_fecha_final
    } = req.body;
    const userToken = req.headers.authorization;

    // Required fields
    const requiredFields = [
      "proceso_id",
      "tipo",
      "etapa1_fecha_inicial",
      "etapa1_fecha_final",
      "etapa2_fecha_inicial",
      "etapa2_fecha_final",
      "etapa3_fecha_inicial", 
      "etapa3_fecha_final"
    ];

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredFields
    );

    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: `Missing required attributes: ${missingAttributes.join(", ")}`,
      });
    }

    // Verify that the proceso_id exists
    const proceso = await OAProceso.findByPk(proceso_id);
    if (!proceso) {
      return res.status(404).json({ message: "Proceso not found" });
    }

    // Get creator email from token
    const creador_email = await getEmailFromToken(userToken);

    // Find creator in database
    const creador = await Usuario.findOne({
      where: { email: { [Op.iLike]: creador_email } },
    });

    if (!creador) {
      return res.status(404).json({ message: "Usuario not found" });
    }

    // Create transaction to ensure all operations succeed or fail together
    const result = await sequelize.transaction(async (t) => {
      // Create new subproceso
      const newSubProceso = await OASubProceso.create({
        proceso_id,
        tipo,
        fecha_inicial: etapa1_fecha_inicial,
        fecha_final: etapa2_fecha_final,
        validado: false,
        etapa_actual: 1,
        creado_por: creador.email,
      }, { transaction: t });

      // Categorias
      const categorias = ["pregrado", "postgrado", "postitulo"];
      const etapas = [];

      // Etapa 1 - insumo/carga for each categoria
      for (const categoria of categorias) {
        const etapa1Insumo = await OAEtapa.create({
          subproceso_id: newSubProceso.id,
          tipo: "insumo",
          etapa: 1,
          categoria,
          fecha_inicial: etapa1_fecha_inicial,
          fecha_final: etapa1_fecha_final,
          validado: false
        }, { transaction: t });
        etapas.push(etapa1Insumo);

        const etapa1Carga = await OAEtapa.create({
          subproceso_id: newSubProceso.id,
          tipo: "carga",
          etapa: 1,
          categoria,
          fecha_inicial: etapa1_fecha_inicial,
          fecha_final: etapa1_fecha_final,
          validado: false
        }, { transaction: t });
        etapas.push(etapa1Carga);
      }

      // Etapa 2 - insumo/carga for each categoria
      for (const categoria of categorias) {
        const etapa2Insumo = await OAEtapa.create({
          subproceso_id: newSubProceso.id,
          tipo: "insumo",
          etapa: 2,
          categoria,
          fecha_inicial: etapa2_fecha_inicial,
          fecha_final: etapa2_fecha_final,
          validado: false
        }, { transaction: t });
        etapas.push(etapa2Insumo);

        const etapa2Carga = await OAEtapa.create({
          subproceso_id: newSubProceso.id,
          tipo: "carga",
          etapa: 2,
          categoria,
          fecha_inicial: etapa2_fecha_inicial,
          fecha_final: etapa2_fecha_final,
          validado: false
        }, { transaction: t });
        etapas.push(etapa2Carga);
      }
      // Etapa 3 - insumo/carga for each categoria
      for (const categoria of categorias) {
        const etapa3Insumo = await OAEtapa.create({
          subproceso_id: newSubProceso.id,
          tipo: "insumo",
          etapa: 3,
          categoria,
          fecha_inicial: req.body.etapa3_fecha_inicial,
          fecha_final: req.body.etapa3_fecha_final,
          validado: false
        }, { transaction: t });
        etapas.push(etapa3Insumo);

        const etapa3Carga = await OAEtapa.create({
          subproceso_id: newSubProceso.id,
          tipo: "carga",
          etapa: 3,
          categoria,
          fecha_inicial: req.body.etapa3_fecha_inicial,
          fecha_final: req.body.etapa3_fecha_final,
          validado: false
        }, { transaction: t });
        etapas.push(etapa3Carga);
      }
      
      return {
        subproceso: newSubProceso,
        etapas
      };
    });

    // Fetch the created subproceso with its associated proceso and etapas
    const createdSubProceso = await OASubProceso.findByPk(result.subproceso.id, {
      include: [
        {
          model: OAProceso,
          attributes: ['id', 'anio', 'fecha_inicial', 'fecha_final']
        },
        {
          model: OAEtapa,
          attributes: ['id', 'tipo', 'etapa', 'categoria', 'fecha_inicial', 'fecha_final', 'validado']
        }
      ]
    });

    res.status(201).json({
      message: "SubProceso with etapas created successfully",
      subproceso: createdSubProceso,
    });
  } catch (error) {
    console.error("Error creating subproceso with etapas:", error);
    res.status(500).json({ message: "Error creating subproceso with etapas", error: error.message });
  }
};