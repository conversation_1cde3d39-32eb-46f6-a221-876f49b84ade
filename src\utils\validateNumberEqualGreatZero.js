/**
 * Validates an array of numbers to ensure all are >= 0 and <= Number.MAX_SAFE_INTEGER.
 * @param {number[]} numbers - Array of numbers to validate.
 * @returns {boolean} - Returns true if all numbers are valid, otherwise false.
 */
const validateNumbers = async (numbers) => {
  const maxInt = 900000; // Maximum safe integer value in JavaScript
  console.log("maxInt", maxInt);
  // Loop through each number in the array
  for (let num of numbers) {
    // Check if the value is not a number
    if (typeof num !== "number") {
      return false; // Invalid if it's not a number
    }

    // Check if the number is less than 0 or greater than the maximum safe integer
    if (num < 0 || num > maxInt) {
      return false; // Invalid if out of range
    }
  }

  // If all numbers pass the checks, return true
  return true;
};

export default validateNumbers;
