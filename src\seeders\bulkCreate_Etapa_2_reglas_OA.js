const rulesPregrado2 = [
  {
    nombre_campo: "COD_SEDE",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "NOMBRE_SEDE",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "alfanumerico",
    regex: "^[A-Z0-9]+( [A-Z0-9]+)*$",
  },
  {
    nombre_campo: "COD_CARRERA",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "vacio",
    regex: "^$",
  },
  {
    nombre_campo: "NOMBRE_CARRERA",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "alfanumerico",
    regex: "^[A-Z0-9]+( [A-Z0-9]+)*$",
  },
  {
    nombre_campo: "MODALIDAD",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-3]$",
  },
  {
    nombre_campo: "COD_JORNADA",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-4]$",
  },
  {
    nombre_campo: "VERSION",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-9]+$",
  },
  {
    nombre_campo: "COD_TIPO_PLAN_CARRERA",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-3]$",
  },
  {
    nombre_campo: "CARACTERISTICAS_TIPO_PLAN",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "alfanumerico",
    regex: "^[A-Z0-9]+( [A-Z0-9]+)*$",
  },
  {
    nombre_campo: "DURACION_ESTUDIOS",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "DURACION_TITULACION",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "DURACION_TOTAL",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "REGIMEN",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-4]$",
  },
  {
    nombre_campo: "DURACION_REGIMEN",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "NOMBRE_TITULO",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "alfanumerico",
    regex: "^[A-Z0-9]+( [A-Z0-9]+)*$",
  },
  {
    nombre_campo: "NOMBRE_GRADO",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "alfanumerico",
    regex: "^[A-Z0-9]+( [A-Z0-9]+)*$",
  },
  {
    nombre_campo: "COD_NIVEL_GLOBAL",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-3]$",
  },
  {
    nombre_campo: "COD_NIVEL_CARRERA",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0-9]$",
  },
  {
    nombre_campo: "COD_DEMRE",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0+$",
  },
  //checkear desde aqui
  {
    nombre_campo: "ANIO_INICIO",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "ACREDITACION",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-2]$",
  },
  {
    nombre_campo: "ELEGIBLE_BECA_PEDAGOGIA",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0-3]$",
  },
  {
    nombre_campo: "PED_MED_ODONT_OTRO",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "char",
    regex: "^[PMDO]$",
  },
  {
    nombre_campo: "REQUISITO_INGRESO",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^(10|[1-9])$",
  },
  {
    nombre_campo: "SEMESTRES_RECONOCIDOS",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "AREA_ACTUAL",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-8]$",
  },
  {
    nombre_campo: "AREA_DESTINO_AGRICULTURA",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0-1]$",
  },
  {
    nombre_campo: "AREA_DESTINO_CIENCIAS",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0-1]$",
  },
  {
    nombre_campo: "AREA_DESTINO_CS_SOCIALES",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0-1]$",
  },
  {
    nombre_campo: "AREA_DESTINO_EDUCACION",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0-1]$",
  },
  {
    nombre_campo: "AREA_DESTINO_HUMANIDADES",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0-1]$",
  },
  {
    nombre_campo: "AREA_DESTINO_INGENIERIA",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0-1]$",
  },
  {
    nombre_campo: "AREA_DESTINO_SALUD",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0-1]$",
  },
  {
    nombre_campo: "AREA_DESTINO_SERVICIOS",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0-1]$",
  },
  {
    nombre_campo: "PONDERACION_NEM",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^(100|[1-9]?[0-9])$",
  },
  {
    nombre_campo: "PONDERACION_RANKING",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^(100|[1-9]?[0-9])$",
  },
  {
    nombre_campo: "PONDERACION_C_LECTORA",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^(100|[1-9]?[0-9])$",
  },
  {
    nombre_campo: "PONDERACION_MATEMATICAS_1",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^(100|[1-9]?[0-9])$",
  },
  {
    nombre_campo: "PONDERACION_MATEMATICAS_2",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^(100|[1-9]?[0-9])$",
  },
  {
    nombre_campo: "PONDERACION_HISTORIA",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^(100|[1-9]?[0-9])$",
  },
  {
    nombre_campo: "PONDERACION_CIENCIAS",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^(100|[1-9]?[0-9])$",
  },
  {
    nombre_campo: "PONDERACION_OTROS",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^(100|[1-9]?[0-9])$",
  },
  {
    nombre_campo: "VACANTES_PRIMER_SEMESTRE",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "VACANTES_SEGUNDO_SEMESTRE",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "VACANTES_PACE",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "MALLA_CURRICULAR",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "alfanumerico",
    regex: "^[a-zA-Z0-9._~:/?#\\[\\]@!$&'()*+,;=%-]+$",
  },
  {
    nombre_campo: "PERFIL_EGRESO",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[A-Z0-9]+( [A-Z0-9]+)*$",
  },
  {
    nombre_campo: "TEXTO_REQUISITO_INGRESO",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[A-Z0-9]+( [A-Z0-9]+)*$",
  },
  {
    nombre_campo: "OTROS_REQUISITOS",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "alfanumerico",
    regex: "^[A-Z0-9]+( [A-Z0-9]+)*$",
  },
  {
    nombre_campo: "MAIL_DIFUSION_CARRERA",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "alfanumerico",
    regex: "^[a-zA-Z0-9._~:/?#\\[\\]@!$&'()*+,;=%-]+$",
  },
  {
    nombre_campo: "VIGENCIA_CARRERA",
    etapa_actual: "2",
    tipo_acceso: "pregrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-3]$",
  },
];
const rulesPosgrado2 = [
  {
    nombre_campo: "COD_SEDE",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "NOMBRE_SEDE",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "alfanumerico",
    regex: "^[A-Z0-9]+( [A-Z0-9]+)*$",
  },
  {
    nombre_campo: "COD_CARRERA",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "vacio",
    regex: "^$",
  },
  {
    nombre_campo: "NOMBRE_CARRERA",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "alfanumerico",
    regex: "^[A-Z0-9]+( [A-Z0-9]+)*$",
  },
  {
    nombre_campo: "MODALIDAD",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-3]$",
  },
  {
    nombre_campo: "COD_JORNADA",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-4]$",
  },
  {
    nombre_campo: "VERSION",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-9]+$",
  },
  {
    nombre_campo: "COD_TIPO_PLAN_CARRERA",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-3]$",
  },
  {
    nombre_campo: "CARACTERISTICAS_TIPO_PLAN",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "alfanumerico",
    regex: "^[A-Z0-9]+( [A-Z0-9]+)*$",
  },
  {
    nombre_campo: "DURACION_ESTUDIOS",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "DURACION_TITULACION",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "DURACION_TOTAL",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "REGIMEN",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-4]$",
  },
  {
    nombre_campo: "DURACION_REGIMEN",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "NOMBRE_TITULO",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "alfanumerico",
    regex: "^[A-Z0-9]+( [A-Z0-9]+)*$",
  },
  {
    nombre_campo: "NOMBRE_GRADO",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "alfanumerico",
    regex: "^[A-Z0-9]+( [A-Z0-9]+)*$",
  },
  {
    nombre_campo: "COD_NIVEL_GLOBAL",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-3]$",
  },
  {
    nombre_campo: "COD_NIVEL_CARRERA",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0-9]$",
  },
  {
    nombre_campo: "COD_DEMRE",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0+$",
  },
  {
    nombre_campo: "ANIO_INICIO",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "ACREDITACION",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-2]$",
  },
  {
    nombre_campo: "ELEGIBLE_BECA_PEDAGOGIA",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0-3]$",
  },
  {
    nombre_campo: "PED_MED_ODONT_OTRO",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "char",
    regex: "^[PMDO]$",
  },
  {
    nombre_campo: "REQUISITO_INGRESO",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^(10|[1-9])$",
  },
  {
    nombre_campo: "SEMESTRES_RECONOCIDOS",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "AREA_ACTUAL",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-8]$",
  },
  {
    nombre_campo: "AREA_DESTINO_AGRICULTURA",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "AREA_DESTINO_CIENCIAS",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "AREA_DESTINO_CS_SOCIALES",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "AREA_DESTINO_EDUCACION",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "AREA_DESTINO_HUMANIDADES",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "AREA_DESTINO_INGENIERIA",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "AREA_DESTINO_SALUD",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "AREA_DESTINO_SERVICIOS",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "PONDERACION_NEM",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "PONDERACION_RANKING",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "PONDERACION_C_LECTORA",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "PONDERACION_MATEMATICAS_1",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "PONDERACION_MATEMATICAS_2",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "PONDERACION_HISTORIA",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "PONDERACION_CIENCIAS",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "PONDERACION_OTROS",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "VACANTES_PRIMER_SEMESTRE",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "VACANTES_SEGUNDO_SEMESTRE",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "VACANTES_PACE",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "MALLA_CURRICULAR",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "vacio",
    regex: "^$",
  },
  {
    nombre_campo: "PERFIL_EGRESO",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^$",
  },
  {
    nombre_campo: "TEXTO_REQUISITO_INGRESO",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "vacio",
    regex: "^$",
  },
  {
    nombre_campo: "OTROS_REQUISITOS",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "vacio",
    regex: "^$",
  },
  {
    nombre_campo: "MAIL_DIFUSION_CARRERA",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "vacio",
    regex: "^$",
  },
  {
    nombre_campo: "VIGENCIA_CARRERA",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-3]$",
  },
];
const rulesPostitulo2 = [
  {
    nombre_campo: "COD_SEDE",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "NOMBRE_SEDE",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "alfanumerico",
    regex: "^[A-Z0-9]+( [A-Z0-9]+)*$",
  },
  {
    nombre_campo: "COD_CARRERA",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "vacio",
    regex: "^$",
  },
  {
    nombre_campo: "NOMBRE_CARRERA",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "alfanumerico",
    regex: "^[A-Z0-9]+( [A-Z0-9]+)*$",
  },
  {
    nombre_campo: "MODALIDAD",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-3]$",
  },
  {
    nombre_campo: "COD_JORNADA",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-4]$",
  },
  {
    nombre_campo: "VERSION",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-9]+$",
  },
  {
    nombre_campo: "COD_TIPO_PLAN_CARRERA",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-3]$",
  },
  {
    nombre_campo: "CARACTERISTICAS_TIPO_PLAN",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "alfanumerico",
    regex: "^[A-Z0-9]+( [A-Z0-9]+)*$",
  },
  {
    nombre_campo: "DURACION_ESTUDIOS",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "DURACION_TITULACION",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "DURACION_TOTAL",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "REGIMEN",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-4]$",
  },
  {
    nombre_campo: "DURACION_REGIMEN",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "NOMBRE_TITULO",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "alfanumerico",
    regex: "^[A-Z0-9]+( [A-Z0-9]+)*$",
  },
  {
    nombre_campo: "NOMBRE_GRADO",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "alfanumerico",
    regex: "^[A-Z0-9]+( [A-Z0-9]+)*$",
  },
  {
    nombre_campo: "COD_NIVEL_GLOBAL",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-3]$",
  },
  {
    nombre_campo: "COD_NIVEL_CARRERA",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0-9]$",
  },
  {
    nombre_campo: "COD_DEMRE",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0+$",
  },
  {
    nombre_campo: "ANIO_INICIO",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "ACREDITACION",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-2]$",
  },
  {
    nombre_campo: "ELEGIBLE_BECA_PEDAGOGIA",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[0-3]$",
  },
  {
    nombre_campo: "PED_MED_ODONT_OTRO",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "char",
    regex: "^[PMDO]$",
  },
  {
    nombre_campo: "REQUISITO_INGRESO",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^(10|[1-9])$",
  },
  {
    nombre_campo: "SEMESTRES_RECONOCIDOS",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "AREA_ACTUAL",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-8]$",
  },
  {
    nombre_campo: "AREA_DESTINO_AGRICULTURA",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "AREA_DESTINO_CIENCIAS",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "AREA_DESTINO_CS_SOCIALES",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "AREA_DESTINO_EDUCACION",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "AREA_DESTINO_HUMANIDADES",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "AREA_DESTINO_INGENIERIA",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "AREA_DESTINO_SALUD",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "AREA_DESTINO_SERVICIOS",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "PONDERACION_NEM",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "PONDERACION_RANKING",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "PONDERACION_C_LECTORA",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "PONDERACION_MATEMATICAS_1",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "PONDERACION_MATEMATICAS_2",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "PONDERACION_HISTORIA",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "PONDERACION_CIENCIAS",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "PONDERACION_OTROS",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "VACANTES_PRIMER_SEMESTRE",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "VACANTES_SEGUNDO_SEMESTRE",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^\\d+$",
  },
  {
    nombre_campo: "VACANTES_PACE",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^0$",
  },
  {
    nombre_campo: "MALLA_CURRICULAR",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "vacio",
    regex: "^$",
  },
  {
    nombre_campo: "PERFIL_EGRESO",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^$",
  },
  {
    nombre_campo: "TEXTO_REQUISITO_INGRESO",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "vacio",
    regex: "^$",
  },
  {
    nombre_campo: "OTROS_REQUISITOS",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "vacio",
    regex: "^$",
  },
  {
    nombre_campo: "MAIL_DIFUSION_CARRERA",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "vacio",
    regex: "^$",
  },
  {
    nombre_campo: "VIGENCIA_CARRERA",
    etapa_actual: "2",
    tipo_acceso: "posgrado",
    es_demre: true,
    obligatorio: true,
    permite_cambios: true,
    especificaciones: "numero",
    regex: "^[1-3]$",
  },
];

export { rulesPregrado2, rulesPosgrado2, rulesPostitulo2 };
